import React, { useState, useEffect } from 'react';

const ProfilePicture = ({
  user,
  size = 'md',
  showOnlineStatus = true,
  className = '',
  onClick = null,
  style = {},
  ...props
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isOnline, setIsOnline] = useState(false);

  // Enhanced online status check
  useEffect(() => {
    if (user && showOnlineStatus) {
      // Check if user was active recently (within last 5 minutes)
      const lastSeen = user.lastSeen ? new Date(user.lastSeen) : null;
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      setIsOnline(lastSeen && lastSeen > fiveMinutesAgo);
    }
  }, [user, showOnlineStatus]);

  const getSizeConfig = () => {
    switch (size) {
      case 'xs':
        return {
          container: 'w-6 h-6',
          text: 'text-xs font-semibold',
          pixels: 24,
          onlineSize: 8,
          border: 'border-2'
        };
      case 'sm':
        return {
          container: 'w-8 h-8',
          text: 'text-sm font-semibold',
          pixels: 32,
          onlineSize: 10,
          border: 'border-2'
        };
      case 'md':
        return {
          container: 'w-12 h-12',
          text: 'text-base font-bold',
          pixels: 48,
          onlineSize: 12,
          border: 'border-2'
        };
      case 'lg':
        return {
          container: 'w-16 h-16',
          text: 'text-lg font-bold',
          pixels: 64,
          onlineSize: 16,
          border: 'border-3'
        };
      case 'xl':
        return {
          container: 'w-20 h-20',
          text: 'text-xl font-bold',
          pixels: 80,
          onlineSize: 18,
          border: 'border-3'
        };
      case '2xl':
        return {
          container: 'w-24 h-24',
          text: 'text-2xl font-bold',
          pixels: 96,
          onlineSize: 20,
          border: 'border-4'
        };
      case '3xl':
        return {
          container: 'w-32 h-32',
          text: 'text-3xl font-bold',
          pixels: 128,
          onlineSize: 24,
          border: 'border-4'
        };
      default:
        return {
          container: 'w-12 h-12',
          text: 'text-base font-bold',
          pixels: 48,
          onlineSize: 12,
          border: 'border-2'
        };
    }
  };

  const sizeConfig = getSizeConfig();
  const isClickable = onClick !== null;

  // Generate user initials
  const getInitials = (user) => {
    if (!user) return '?';
    const name = user.name || user.username || 'User';
    const words = name.trim().split(' ');
    if (words.length >= 2) {
      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
    }
    return name.charAt(0).toUpperCase();
  };

  // Generate consistent color based on user name
  const getAvatarColor = (user) => {
    if (!user) return '#6B7280'; // Gray for unknown user

    const name = user.name || user.username || 'User';
    const colors = [
      '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',
      '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',
      '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',
      '#EC4899', '#F43F5E'
    ];

    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    return colors[Math.abs(hash) % colors.length];
  };

  return (
    <div 
      className={`relative inline-block ${className}`} 
      style={{ padding: showOnlineStatus ? '2px' : '0' }}
      {...props}
    >
      <div
        className={`
          ${sizeClasses.container}
          rounded-full overflow-hidden border-2 border-white/20 relative
          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}
        `}
        style={{
          background: '#f0f0f0',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          ...style
        }}
        onClick={onClick}
      >
        {!user ? (
          // Show fallback for undefined user
          <div
            className={`
              rounded-full flex items-center justify-center font-semibold w-full h-full
              ${sizeClasses.text}
            `}
            style={{
              background: '#25D366',
              color: '#FFFFFF'
            }}
          >
            ?
          </div>
        ) : user?.profileImage || user?.profilePicture ? (
          <img
            src={user.profileImage || user.profilePicture}
            alt={user.name || 'User'}
            className="object-cover rounded-full w-full h-full"
            style={{ objectFit: 'cover' }}
            onError={(e) => {
              // Fallback to initials if image fails to load
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
        ) : null}
        
        {/* Fallback initials - only show if user exists and has no profile image */}
        {user && !(user?.profileImage || user?.profilePicture) && (
          <div
            className={`
              rounded-full flex items-center justify-center font-semibold w-full h-full
              ${sizeClasses.text}
            `}
            style={{
              background: '#25D366',
              color: '#FFFFFF'
            }}
          >
            {(user?.name || user?.username || 'U').charAt(0).toUpperCase()}
          </div>
        )}
      </div>

      {/* Online Status Indicator - Only show if actually online */}
      {showOnlineStatus && user?._id && !loading && isOnline && (
        <div
          style={{
            position: 'absolute',
            width: size === 'xs' ? '8px' :
                   size === 'sm' ? '10px' :
                   size === 'md' ? '12px' :
                   size === 'lg' ? '14px' :
                   size === 'xl' ? '16px' :
                   size === '2xl' ? '18px' :
                   size === '3xl' ? '20px' : '12px',
            height: size === 'xs' ? '8px' :
                    size === 'sm' ? '10px' :
                    size === 'md' ? '12px' :
                    size === 'lg' ? '14px' :
                    size === 'xl' ? '16px' :
                    size === '2xl' ? '18px' :
                    size === '3xl' ? '20px' : '12px',
            bottom: '-3px',
            right: '-3px',
            zIndex: 999,
            borderRadius: '50%',
            backgroundColor: '#22c55e',
            background: '#22c55e',
            border: '2px solid #1f2937',
            outline: 'none',
            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.8)',
            animation: 'pulse 2s infinite'
          }}
          title="Online"
        />
      )}
    </div>
  );
};

export default ProfilePicture;
