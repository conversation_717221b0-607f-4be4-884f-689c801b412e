{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link, useLocation } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useSelector } from \"react-redux\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport NotificationBell from \"../../../components/common/NotificationBell\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const [menuOpen, setMenuOpen] = useState(false);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const location = useLocation();\n\n  // Check if user is logged in\n  const isLoggedIn = !!user;\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n      // Close mobile menu after clicking\n      setMenuOpen(false);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home\",\n    children: [/*#__PURE__*/_jsxDEV(motion.header, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden lg:flex items-center space-x-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  className: \"w-4 h-4 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-700\",\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-700\",\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  className: \"w-4 h-4 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-700\",\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 93,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex lg:hidden items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1 text-xs\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  className: \"w-3 h-3 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-700\",\n                  children: \"15K+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1 text-xs\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  className: \"w-3 h-3 text-yellow-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-700\",\n                  children: \"4.9/5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.9\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"relative group flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\",\n                style: {\n                  width: '32px',\n                  height: '24px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://flagcdn.com/w40/tz.png\",\n                  alt: \"Tanzania Flag\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    // Fallback to another flag source if first fails\n                    e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\n                    e.target.onerror = () => {\n                      // Final fallback - hide image and show text\n                      e.target.style.display = 'none';\n                      e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\n                    };\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      y: [0, -2, 0, 2, 0],\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.5,\n                      y: {\n                        duration: 3,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      },\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#059669',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        x: [0, 40, 80],\n                        y: [0, -5, 0, 5, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 3,\n                        repeat: Infinity,\n                        delay: 1\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 248,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rounded-full overflow-hidden border-2 border-white/20 relative\",\n                style: {\n                  background: '#f0f0f0',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n                  width: '32px',\n                  height: '32px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  style: {\n                    objectFit: 'cover'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\",\n                  style: {\n                    display: 'none',\n                    fontSize: '12px'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-end space-x-3 sm:space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"hidden md:block nav-item text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-300\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), user ? /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 group\",\n              children: [!(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: user,\n                size: \"sm\",\n                showOnlineStatus: true,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\",\n                  children: [\"Class \", user === null || user === void 0 ? void 0 : user.class]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 17\n            }, this) :\n            /*#__PURE__*/\n            /* Login and Registration Buttons - if not logged in */\n            _jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 border border-blue-600 hover:border-blue-700 rounded-lg transition-all duration-300\",\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"px-3 py-1.5 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg\",\n                children: \"Registration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"hero-content\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"hero-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs sm:text-sm\",\n                children: \"#1 Educational Platform in Tanzania\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"hero-title\",\n              children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gradient\",\n                children: [\"Education\", /*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                  className: \"inline w-8 h-8 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"hero-subtitle\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"cta-section\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary btn-large\",\n                  children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-5 h-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 444,\n                    columnNumber: 21\n                  }, this), \"Explore Platform\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), !user && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.5\n              },\n              className: \"flex flex-col sm:flex-row items-center justify-center gap-3 mt-6\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"w-full sm:w-auto px-6 py-3 text-base font-medium text-blue-600 bg-white border-2 border-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-md hover:shadow-lg text-center\",\n                children: \"Login to Your Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"w-full sm:w-auto px-6 py-3 text-base font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg text-center\",\n                children: \"Create New Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"trust-indicators\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"hero-image\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: Image1,\n                alt: \"Students Learning\",\n                loading: \"lazy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  top: '-1rem',\n                  left: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  bottom: '-1rem',\n                  right: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 401,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"stats-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"stats-grid\",\n          children: [{\n            number: \"15K+\",\n            text: \"Active Students\"\n          }, {\n            number: \"500+\",\n            text: \"Expert Teachers\"\n          }, {\n            number: \"1000+\",\n            text: \"Video Lessons\"\n          }, {\n            number: \"98%\",\n            text: \"Success Rate\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-text\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 534,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"reviews-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"reviews-title\",\n          children: \"Reviews from our students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-grid\",\n          children: [{\n            rating: 5,\n            text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\n            user: {\n              name: \"Sarah Johnson\"\n            }\n          }, {\n            rating: 5,\n            text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\n            user: {\n              name: \"Michael Chen\"\n            }\n          }, {\n            rating: 5,\n            text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\n            user: {\n              name: \"Amina Hassan\"\n            }\n          }].map((review, index) => {\n            var _review$user;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"review-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-rating\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#f59e0b',\n                    fontSize: '1.25rem'\n                  },\n                  children: '★'.repeat(review.rating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-text\",\n                children: [\"\\\"\", review.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-divider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-author\",\n                children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"contact-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"contact-title\",\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"contact-subtitle\",\n          children: \"Get in touch with us for any questions or support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"contact-form\",\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"name\",\n              placeholder: \"Your Name\",\n              className: \"form-input\",\n              value: formData.name,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              placeholder: \"Your Email\",\n              className: \"form-input\",\n              value: formData.email,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 642,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"message\",\n              placeholder: \"Your Message\",\n              className: \"form-input form-textarea\",\n              value: formData.message,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 643,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"form-submit\",\n            disabled: loading,\n            children: loading ? \"Sending...\" : \"Send Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 652,\n            columnNumber: 13\n          }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"response-message\",\n            style: {\n              marginTop: '1rem',\n              textAlign: 'center',\n              color: '#10b981'\n            },\n            children: responseMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 BrainWave Educational Platform. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 670,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 669,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"tpNN8ex5F56Wk0fjJJpI7CAvrMk=\", false, function () {\n  return [useSelector, useLocation];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "Link", "useLocation", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "message", "useSelector", "Image1", "contactUs", "NotificationBell", "ProfilePicture", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "reviewsSectionRef", "contactUsRef", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "menuOpen", "setMenuOpen", "user", "state", "location", "isLoggedIn", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "data", "success", "error", "className", "children", "header", "initial", "y", "opacity", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "scale", "transition", "duration", "delay", "style", "width", "height", "src", "alt", "objectFit", "onError", "onerror", "display", "parentElement", "innerHTML", "fontFamily", "letterSpacing", "span", "x", "textShadow", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "backgroundColor", "boxShadow", "background", "nextS<PERSON>ling", "fontSize", "onClick", "isAdmin", "size", "showOnlineStatus", "class", "to", "left", "bottom", "right", "whileInView", "viewport", "once", "number", "text", "map", "stat", "index", "rating", "review", "_review$user", "onSubmit", "type", "placeholder", "onChange", "required", "disabled", "marginTop", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link, useLocation } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useSelector } from \"react-redux\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport NotificationBell from \"../../../components/common/NotificationBell\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const [menuOpen, setMenuOpen] = useState(false);\r\n  const { user } = useSelector((state) => state.user);\r\n  const location = useLocation();\r\n\r\n  // Check if user is logged in\r\n  const isLoggedIn = !!user;\r\n\r\n\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n      // Close mobile menu after clicking\r\n      setMenuOpen(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Modern Responsive Header - Same as ProtectedRoute */}\r\n      <motion.header\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20\"\r\n      >\r\n        <div className=\"px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10\">\r\n          <div className=\"flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20\">\r\n            {/* Left section - Trust Indicators */}\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"hidden lg:flex items-center space-x-6\">\r\n                <div className=\"flex items-center space-x-2 text-sm\">\r\n                  <TbUsers className=\"w-4 h-4 text-blue-600\" />\r\n                  <span className=\"font-semibold text-gray-700\">15K+ Students</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2 text-sm\">\r\n                  <TbStar className=\"w-4 h-4 text-yellow-500\" />\r\n                  <span className=\"font-semibold text-gray-700\">4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-2 text-sm\">\r\n                  <TbTrophy className=\"w-4 h-4 text-blue-600\" />\r\n                  <span className=\"font-semibold text-gray-700\">Award Winning</span>\r\n                </div>\r\n              </div>\r\n              {/* Mobile version - condensed */}\r\n              <div className=\"flex lg:hidden items-center space-x-3\">\r\n                <div className=\"flex items-center space-x-1 text-xs\">\r\n                  <TbUsers className=\"w-3 h-3 text-blue-600\" />\r\n                  <span className=\"font-semibold text-gray-700\">15K+</span>\r\n                </div>\r\n                <div className=\"flex items-center space-x-1 text-xs\">\r\n                  <TbStar className=\"w-3 h-3 text-yellow-500\" />\r\n                  <span className=\"font-semibold text-gray-700\">4.9/5</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <motion.div\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"relative group flex items-center space-x-3\"\r\n              >\r\n                {/* Tanzania Flag - Using actual flag image */}\r\n                <div\r\n                  className=\"rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative\"\r\n                  style={{\r\n                    width: '32px',\r\n                    height: '24px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"https://flagcdn.com/w40/tz.png\"\r\n                    alt=\"Tanzania Flag\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      // Fallback to another flag source if first fails\r\n                      e.target.src = \"https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png\";\r\n                      e.target.onerror = () => {\r\n                        // Final fallback - hide image and show text\r\n                        e.target.style.display = 'none';\r\n                        e.target.parentElement.innerHTML = '<div class=\"w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold\">TZ</div>';\r\n                      };\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects (no space) */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        y: [0, -2, 0, 2, 0],\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.5,\r\n                        y: {\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        },\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#059669',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particle */}\r\n                      <motion.div\r\n                        className=\"absolute top-0 left-0 w-1.5 h-1.5 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          x: [0, 40, 80],\r\n                          y: [0, -5, 0, 5, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 3,\r\n                          repeat: Infinity,\r\n                          delay: 1\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo - Small like profile */}\r\n                <div\r\n                  className=\"rounded-full overflow-hidden border-2 border-white/20 relative\"\r\n                  style={{\r\n                    background: '#f0f0f0',\r\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\r\n                    width: '32px',\r\n                    height: '32px'\r\n                  }}\r\n                >\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    style={{ objectFit: 'cover' }}\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div\r\n                    className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold\"\r\n                    style={{\r\n                      display: 'none',\r\n                      fontSize: '12px'\r\n                    }}\r\n                  >\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Glow Effect */}\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110\"></div>\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Right Section - Contact Us + Auth Buttons */}\r\n            <div className=\"flex items-center justify-end space-x-3 sm:space-x-4\">\r\n              {/* Contact Us Button */}\r\n              <button\r\n                onClick={() => scrollToSection(contactUsRef)}\r\n                className=\"hidden md:block nav-item text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-300\"\r\n              >\r\n                Contact Us\r\n              </button>\r\n\r\n              {/* User Profile Section - if logged in */}\r\n              {user ? (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 group\"\r\n                >\r\n                  {/* Notification Bell */}\r\n                  {!user?.isAdmin && (\r\n                    <NotificationBell />\r\n                  )}\r\n\r\n                  {/* Profile Picture with Online Status */}\r\n                  <ProfilePicture\r\n                    user={user}\r\n                    size=\"sm\"\r\n                    showOnlineStatus={true}\r\n                    style={{\r\n                      width: '32px',\r\n                      height: '32px'\r\n                    }}\r\n                  />\r\n\r\n                  {/* User Name and Class */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300\">\r\n                      Class {user?.class}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              ) : (\r\n                /* Login and Registration Buttons - if not logged in */\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 border border-blue-600 hover:border-blue-700 rounded-lg transition-all duration-300\"\r\n                  >\r\n                    Login\r\n                  </Link>\r\n                  <Link\r\n                    to=\"/register\"\r\n                    className=\"px-3 py-1.5 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg\"\r\n                  >\r\n                    Registration\r\n                  </Link>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.header>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"hero-content\"\r\n            >\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"hero-badge\"\r\n              >\r\n                <TbSchool className=\"w-4 h-4 sm:w-5 sm:h-5 mr-2\" />\r\n                <span className=\"text-xs sm:text-sm\">#1 Educational Platform in Tanzania</span>\r\n              </motion.div>\r\n\r\n              <h1 className=\"hero-title\">\r\n                Fueling Bright Futures with{\" \"}\r\n                <span className=\"text-gradient\">\r\n                  Education\r\n                  <TbArrowBigRightLinesFilled className=\"inline w-8 h-8 ml-2\" />\r\n                </span>\r\n              </h1>\r\n\r\n              <p className=\"hero-subtitle\">\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </p>\r\n\r\n              {/* Explore Platform Button */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-section\"\r\n              >\r\n                <Link to=\"/login\">\r\n                  <button className=\"btn btn-primary btn-large\">\r\n                    <TbBrain className=\"w-5 h-5 mr-2\" />\r\n                    Explore Platform\r\n                  </button>\r\n                </Link>\r\n              </motion.div>\r\n\r\n              {/* Login/Register buttons for non-logged in users */}\r\n              {!user && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.6, delay: 0.5 }}\r\n                  className=\"flex flex-col sm:flex-row items-center justify-center gap-3 mt-6\"\r\n                >\r\n                  <Link\r\n                    to=\"/login\"\r\n                    className=\"w-full sm:w-auto px-6 py-3 text-base font-medium text-blue-600 bg-white border-2 border-blue-600 rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-md hover:shadow-lg text-center\"\r\n                  >\r\n                    Login to Your Account\r\n                  </Link>\r\n                  <Link\r\n                    to=\"/register\"\r\n                    className=\"w-full sm:w-auto px-6 py-3 text-base font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg text-center\"\r\n                  >\r\n                    Create New Account\r\n                  </Link>\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div className=\"trust-indicator\">\r\n                  <TbUsers style={{color: '#007BFF'}} />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbStar style={{color: '#f59e0b'}} />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbTrophy style={{color: '#007BFF'}} />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative\">\r\n                <img\r\n                  src={Image1}\r\n                  alt=\"Students Learning\"\r\n                  loading=\"lazy\"\r\n                />\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{top: '-1rem', left: '-1rem'}}\r\n                >\r\n                  <TbBook style={{color: '#007BFF'}} />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{bottom: '-1rem', right: '-1rem'}}\r\n                >\r\n                  <TbTrophy style={{color: '#f59e0b'}} />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"stats-section\">\r\n        <div className=\"container\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"stats-grid\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\" },\r\n              { number: \"500+\", text: \"Expert Teachers\" },\r\n              { number: \"1000+\", text: \"Video Lessons\" },\r\n              { number: \"98%\", text: \"Success Rate\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"stat-item\"\r\n              >\r\n                <div className=\"stat-number\">{stat.number}</div>\r\n                <p className=\"stat-text\">{stat.text}</p>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"contact-section\">\r\n        <div className=\"contact-container\">\r\n          <h2 className=\"contact-title\">Contact Us</h2>\r\n          <p className=\"contact-subtitle\">Get in touch with us for any questions or support</p>\r\n          <form className=\"contact-form\" onSubmit={handleSubmit}>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Name</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Your Name\"\r\n                className=\"form-input\"\r\n                value={formData.name}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Email</label>\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Your Email\"\r\n                className=\"form-input\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Message</label>\r\n              <textarea\r\n                name=\"message\"\r\n                placeholder=\"Your Message\"\r\n                className=\"form-input form-textarea\"\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                required\r\n              ></textarea>\r\n            </div>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"form-submit\"\r\n              disabled={loading}\r\n            >\r\n              {loading ? \"Sending...\" : \"Send Message\"}\r\n            </button>\r\n            {responseMessage && (\r\n              <p className=\"response-message\" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>\r\n                {responseMessage}\r\n              </p>\r\n            )}\r\n          </form>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,OAAO,aAAa;AACpB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,cAAc,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsB,iBAAiB,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMuB,YAAY,GAAGvB,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEhB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM;IAAEmC;EAAK,CAAC,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAME,QAAQ,GAAGlC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMmC,UAAU,GAAG,CAAC,CAACH,IAAI;EAIzB,MAAMI,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;MACjE;MACAd,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMe,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEvB,IAAI;MAAEwB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC1B,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAGwB;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBxB,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAMuB,IAAI,GAAG,MAAMxC,SAAS,CAACU,QAAQ,CAAC;MACtC,IAAI8B,IAAI,CAACC,OAAO,EAAE;QAChB5C,OAAO,CAAC4C,OAAO,CAAC,4BAA4B,CAAC;QAC7CxB,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEhB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACLoB,kBAAkB,CAACuB,IAAI,CAAC3C,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAO6C,KAAK,EAAE;MACdzB,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEX,OAAA;IAAKuC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnBxC,OAAA,CAACf,MAAM,CAACwD,MAAM;MACZC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,kKAAkK;MAAAC,QAAA,eAE5KxC,OAAA;QAAKuC,SAAS,EAAC,+CAA+C;QAAAC,QAAA,eAC5DxC,OAAA;UAAKuC,SAAS,EAAC,wEAAwE;UAAAC,QAAA,gBAErFxC,OAAA;YAAKuC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxC,OAAA;cAAKuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDxC,OAAA;gBAAKuC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDxC,OAAA,CAACV,OAAO;kBAACiD,SAAS,EAAC;gBAAuB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7CjD,OAAA;kBAAMuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACNjD,OAAA;gBAAKuC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDxC,OAAA,CAACT,MAAM;kBAACgD,SAAS,EAAC;gBAAyB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CjD,OAAA;kBAAMuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACNjD,OAAA;gBAAKuC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDxC,OAAA,CAACX,QAAQ;kBAACkD,SAAS,EAAC;gBAAuB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CjD,OAAA;kBAAMuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENjD,OAAA;cAAKuC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDxC,OAAA;gBAAKuC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDxC,OAAA,CAACV,OAAO;kBAACiD,SAAS,EAAC;gBAAuB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7CjD,OAAA;kBAAMuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAI;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNjD,OAAA;gBAAKuC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDxC,OAAA,CAACT,MAAM;kBAACgD,SAAS,EAAC;gBAAyB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9CjD,OAAA;kBAAMuC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAAK;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjD,OAAA;YAAKuC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzCxC,OAAA,CAACf,MAAM,CAACiE,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAI,CAAE;cACpCN,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAGtDxC,OAAA;gBACEuC,SAAS,EAAC,wEAAwE;gBAClFgB,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,eAEFxC,OAAA;kBACE0D,GAAG,EAAC,gCAAgC;kBACpCC,GAAG,EAAC,eAAe;kBACnBpB,SAAS,EAAC,4BAA4B;kBACtCgB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG9B,CAAC,IAAK;oBACd;oBACAA,CAAC,CAACE,MAAM,CAACyB,GAAG,GAAG,8GAA8G;oBAC7H3B,CAAC,CAACE,MAAM,CAAC6B,OAAO,GAAG,MAAM;sBACvB;sBACA/B,CAAC,CAACE,MAAM,CAACsB,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/BhC,CAAC,CAACE,MAAM,CAAC+B,aAAa,CAACC,SAAS,GAAG,+GAA+G;oBACpJ,CAAC;kBACH;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNjD,OAAA;gBAAKuC,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3CxC,OAAA;kBAAIuC,SAAS,EAAC,qFAAqF;kBAC/FgB,KAAK,EAAE;oBACLW,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAA3B,QAAA,gBAEJxC,OAAA,CAACf,MAAM,CAACmF,IAAI;oBACV7B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEyB,CAAC,EAAE,CAAC,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC5CN,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVyB,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRmB,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVgB,UAAU,EAAE;wBACVjB,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA9B,QAAA,GACH,OAGC,eACAxC,OAAA,CAACf,MAAM,CAACiE,GAAG;sBACTX,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBO,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtB2B,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdjD,OAAA,CAACf,MAAM,CAACmF,IAAI;oBACV7B,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEyB,CAAC,EAAE,EAAE;sBAAElB,KAAK,EAAE;oBAAI,CAAE;oBAC3CN,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVyB,CAAC,EAAE,CAAC;sBACJlB,KAAK,EAAE,CAAC;sBACRR,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACnB2B,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFlB,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVX,CAAC,EAAE;wBACDU,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR,CAAC;sBACDH,UAAU,EAAE;wBACVjB,QAAQ,EAAE,GAAG;wBACbkB,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVvB,KAAK,EAAE,GAAG;sBACVwB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBvB,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFE,KAAK,EAAE;sBACLqB,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBP,UAAU,EAAE;oBACd,CAAE;oBAAA9B,QAAA,GACH,MAGC,eACAxC,OAAA,CAACf,MAAM,CAACiE,GAAG;sBACTX,SAAS,EAAC,gDAAgD;sBAC1DM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClByB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;wBACd1B,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACnBmC,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACF1B,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXkB,MAAM,EAAEC,QAAQ;wBAChBlB,KAAK,EAAE;sBACT,CAAE;sBACFC,KAAK,EAAE;wBACLuB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLjD,OAAA,CAACf,MAAM,CAACiE,GAAG;kBACTX,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAEc,KAAK,EAAE,CAAC;oBAAEZ,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACPW,KAAK,EAAE,MAAM;oBACbZ,OAAO,EAAE,CAAC;oBACVmC,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACF3B,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVyB,SAAS,EAAE;sBACT1B,QAAQ,EAAE,CAAC;sBACXkB,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFlB,KAAK,EAAE;oBACLyB,UAAU,EAAE,mDAAmD;oBAC/DD,SAAS,EAAE;kBACb;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNjD,OAAA;gBACEuC,SAAS,EAAC,gEAAgE;gBAC1EgB,KAAK,EAAE;kBACLyB,UAAU,EAAE,SAAS;kBACrBD,SAAS,EAAE,4BAA4B;kBACvCvB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV,CAAE;gBAAAjB,QAAA,gBAEFxC,OAAA;kBACE0D,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBpB,SAAS,EAAC,4BAA4B;kBACtCgB,KAAK,EAAE;oBAAEK,SAAS,EAAE;kBAAQ,CAAE;kBAC9BC,OAAO,EAAG9B,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACsB,KAAK,CAACQ,OAAO,GAAG,MAAM;oBAC/BhC,CAAC,CAACE,MAAM,CAACgD,WAAW,CAAC1B,KAAK,CAACQ,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFjD,OAAA;kBACEuC,SAAS,EAAC,gHAAgH;kBAC1HgB,KAAK,EAAE;oBACLQ,OAAO,EAAE,MAAM;oBACfmB,QAAQ,EAAE;kBACZ,CAAE;kBAAA1C,QAAA,EACH;gBAED;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjD,OAAA;gBAAKuC,SAAS,EAAC;cAAyK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNjD,OAAA;YAAKuC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBAEnExC,OAAA;cACEmF,OAAO,EAAEA,CAAA,KAAM/D,eAAe,CAACf,YAAY,CAAE;cAC7CkC,SAAS,EAAC,+GAA+G;cAAAC,QAAA,EAC1H;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAGRjC,IAAI,gBACHhB,OAAA,CAACf,MAAM,CAACiE,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAI,CAAE;cACpCN,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEO,KAAK,EAAE;cAAE,CAAE;cAClCC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,mCAAmC;cAAAC,QAAA,GAG5C,EAACxB,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoE,OAAO,kBACbpF,OAAA,CAACH,gBAAgB;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACpB,eAGDjD,OAAA,CAACF,cAAc;gBACbkB,IAAI,EAAEA,IAAK;gBACXqE,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,IAAK;gBACvB/B,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFjD,OAAA;gBAAKuC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCxC,OAAA;kBAAKuC,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,EACpH,CAAAxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;gBAAM;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNjD,OAAA;kBAAKuC,SAAS,EAAC,iFAAiF;kBAAAC,QAAA,GAAC,QACzF,EAACxB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,KAAK;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;YAAA;YAEb;YACAjD,OAAA;cAAKuC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxC,OAAA,CAACjB,IAAI;gBACHyG,EAAE,EAAC,QAAQ;gBACXjD,SAAS,EAAC,uJAAuJ;gBAAAC,QAAA,EAClK;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjD,OAAA,CAACjB,IAAI;gBACHyG,EAAE,EAAC,WAAW;gBACdjD,SAAS,EAAC,2IAA2I;gBAAAC,QAAA,EACtJ;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGhBjD,OAAA;MAASqB,GAAG,EAAElB,cAAe;MAACoC,SAAS,EAAC,cAAc;MAAAC,QAAA,eACpDxC,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBxC,OAAA;UAAKuC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBxC,OAAA,CAACf,MAAM,CAACiE,GAAG;YACTR,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEyB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCxB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEyB,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9Bd,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExBxC,OAAA,CAACf,MAAM,CAACiE,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BS,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAEtBxC,OAAA,CAACR,QAAQ;gBAAC+C,SAAS,EAAC;cAA4B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnDjD,OAAA;gBAAMuC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAmC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eAEbjD,OAAA;cAAIuC,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,6BACE,EAAC,GAAG,eAC/BxC,OAAA;gBAAMuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,WAE9B,eAAAxC,OAAA,CAACd,0BAA0B;kBAACqD,SAAS,EAAC;gBAAqB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELjD,OAAA;cAAGuC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJjD,OAAA,CAACf,MAAM,CAACiE,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BS,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,aAAa;cAAAC,QAAA,eAEvBxC,OAAA,CAACjB,IAAI;gBAACyG,EAAE,EAAC,QAAQ;gBAAAhD,QAAA,eACfxC,OAAA;kBAAQuC,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBAC3CxC,OAAA,CAACb,OAAO;oBAACoD,SAAS,EAAC;kBAAc;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,EAGZ,CAACjC,IAAI,iBACJhB,OAAA,CAACf,MAAM,CAACiE,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BS,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,kEAAkE;cAAAC,QAAA,gBAE5ExC,OAAA,CAACjB,IAAI;gBACHyG,EAAE,EAAC,QAAQ;gBACXjD,SAAS,EAAC,gMAAgM;gBAAAC,QAAA,EAC3M;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPjD,OAAA,CAACjB,IAAI;gBACHyG,EAAE,EAAC,WAAW;gBACdjD,SAAS,EAAC,wKAAwK;gBAAAC,QAAA,EACnL;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CACb,eAGDjD,OAAA,CAACf,MAAM,CAACiE,GAAG;cACTR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BS,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1Cf,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5BxC,OAAA;gBAAKuC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxC,OAAA,CAACV,OAAO;kBAACiE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCjD,OAAA;kBAAAwC,QAAA,EAAM;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNjD,OAAA;gBAAKuC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxC,OAAA,CAACT,MAAM;kBAACgE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCjD,OAAA;kBAAAwC,QAAA,EAAM;gBAAY;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNjD,OAAA;gBAAKuC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxC,OAAA,CAACX,QAAQ;kBAACkE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCjD,OAAA;kBAAAwC,QAAA,EAAM;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbjD,OAAA,CAACf,MAAM,CAACiE,GAAG;YACTR,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEyB,CAAC,EAAE;YAAG,CAAE;YAC/BxB,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEyB,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1Cf,SAAS,EAAC,YAAY;YAAAC,QAAA,eAEtBxC,OAAA;cAAKuC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBxC,OAAA;gBACE0D,GAAG,EAAE/D,MAAO;gBACZgE,GAAG,EAAC,mBAAmB;gBACvBjD,OAAO,EAAC;cAAM;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGFjD,OAAA,CAACf,MAAM,CAACiE,GAAG;gBACTL,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BS,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC;gBAAS,CAAE;gBAC9CjC,SAAS,EAAC,kBAAkB;gBAC5BgB,KAAK,EAAE;kBAAC3B,GAAG,EAAE,OAAO;kBAAE6D,IAAI,EAAE;gBAAO,CAAE;gBAAAjD,QAAA,eAErCxC,OAAA,CAACZ,MAAM;kBAACmE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEbjD,OAAA,CAACf,MAAM,CAACiE,GAAG;gBACTL,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BS,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEkB,MAAM,EAAEC;gBAAS,CAAE;gBAC9CjC,SAAS,EAAC,kBAAkB;gBAC5BgB,KAAK,EAAE;kBAACmC,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAnD,QAAA,eAEzCxC,OAAA,CAACX,QAAQ;kBAACkE,KAAK,EAAE;oBAACqB,KAAK,EAAE;kBAAS;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAASuC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChCxC,OAAA;QAAKuC,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBxC,OAAA,CAACf,MAAM,CAACiE,GAAG;UACTR,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/BiD,WAAW,EAAE;YAAEhD,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCS,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BwC,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBvD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAErB,CACC;YAAEuD,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAkB,CAAC,EAC3C;YAAED,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAkB,CAAC,EAC3C;YAAED,MAAM,EAAE,OAAO;YAAEC,IAAI,EAAE;UAAgB,CAAC,EAC1C;YAAED,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE;UAAe,CAAC,CACxC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBnG,OAAA,CAACf,MAAM,CAACiE,GAAG;YAETR,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BiD,WAAW,EAAE;cAAEhD,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCS,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE6C,KAAK,GAAG;YAAI,CAAE;YAClDN,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBvD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAErBxC,OAAA;cAAKuC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAE0D,IAAI,CAACH;YAAM;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDjD,OAAA;cAAGuC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAE0D,IAAI,CAACF;YAAI;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GARnCkD,KAAK;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAASqB,GAAG,EAAEjB,iBAAkB;MAACmC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC1DxC,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxC,OAAA;UAAIuC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLjD,OAAA;UAAKuC,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,CACC;YACE4D,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,yIAAyI;YAC/IhF,IAAI,EAAE;cAAER,IAAI,EAAE;YAAgB;UAChC,CAAC,EACD;YACE4F,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,uIAAuI;YAC7IhF,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,EACD;YACE4F,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,mHAAmH;YACzHhF,IAAI,EAAE;cAAER,IAAI,EAAE;YAAe;UAC/B,CAAC,CACF,CAACyF,GAAG,CAAC,CAACI,MAAM,EAAEF,KAAK;YAAA,IAAAG,YAAA;YAAA,oBAClBtG,OAAA,CAACf,MAAM,CAACiE,GAAG;cAETR,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BiD,WAAW,EAAE;gBAAEhD,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAClCS,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE6C,KAAK,GAAG;cAAI,CAAE;cAClDN,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBvD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvBxC,OAAA;gBAAKuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BxC,OAAA;kBAAKuD,KAAK,EAAE;oBAAEqB,KAAK,EAAE,SAAS;oBAAEM,QAAQ,EAAE;kBAAU,CAAE;kBAAA1C,QAAA,EACnD,GAAG,CAAC+B,MAAM,CAAC8B,MAAM,CAACD,MAAM;gBAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNjD,OAAA;gBAAKuC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,IAAC,EAAC6D,MAAM,CAACL,IAAI,EAAC,IAAC;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClDjD,OAAA;gBAAKuC,SAAS,EAAC;cAAgB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCjD,OAAA;gBAAKuC,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAA8D,YAAA,GAAED,MAAM,CAACrF,IAAI,cAAAsF,YAAA,uBAAXA,YAAA,CAAa9F;cAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAdnDkD,KAAK;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAASqB,GAAG,EAAEhB,YAAa;MAACkC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eACrDxC,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxC,OAAA;UAAIuC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CjD,OAAA;UAAGuC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAiD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrFjD,OAAA;UAAMuC,SAAS,EAAC,cAAc;UAACgE,QAAQ,EAAErE,YAAa;UAAAM,QAAA,gBACpDxC,OAAA;YAAKuC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxC,OAAA;cAAOuC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CjD,OAAA;cACEwG,IAAI,EAAC,MAAM;cACXhG,IAAI,EAAC,MAAM;cACXiG,WAAW,EAAC,WAAW;cACvBlE,SAAS,EAAC,YAAY;cACtBP,KAAK,EAAE1B,QAAQ,CAACE,IAAK;cACrBkG,QAAQ,EAAE5E,YAAa;cACvB6E,QAAQ;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjD,OAAA;YAAKuC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxC,OAAA;cAAOuC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CjD,OAAA;cACEwG,IAAI,EAAC,OAAO;cACZhG,IAAI,EAAC,OAAO;cACZiG,WAAW,EAAC,YAAY;cACxBlE,SAAS,EAAC,YAAY;cACtBP,KAAK,EAAE1B,QAAQ,CAACG,KAAM;cACtBiG,QAAQ,EAAE5E,YAAa;cACvB6E,QAAQ;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNjD,OAAA;YAAKuC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBxC,OAAA;cAAOuC,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CjD,OAAA;cACEQ,IAAI,EAAC,SAAS;cACdiG,WAAW,EAAC,cAAc;cAC1BlE,SAAS,EAAC,0BAA0B;cACpCP,KAAK,EAAE1B,QAAQ,CAACb,OAAQ;cACxBiH,QAAQ,EAAE5E,YAAa;cACvB6E,QAAQ;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNjD,OAAA;YACEwG,IAAI,EAAC,QAAQ;YACbjE,SAAS,EAAC,aAAa;YACvBqE,QAAQ,EAAElG,OAAQ;YAAA8B,QAAA,EAEjB9B,OAAO,GAAG,YAAY,GAAG;UAAc;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACRrC,eAAe,iBACdZ,OAAA;YAAGuC,SAAS,EAAC,kBAAkB;YAACgB,KAAK,EAAE;cAAEsD,SAAS,EAAE,MAAM;cAAEC,SAAS,EAAE,QAAQ;cAAElC,KAAK,EAAE;YAAU,CAAE;YAAApC,QAAA,EACjG5B;UAAe;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVjD,OAAA;MAAQuC,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxBxC,OAAA;QAAKuC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BxC,OAAA;UAAGuC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC/C,EAAA,CAjpBID,IAAI;EAAA,QAQSP,WAAW,EACXV,WAAW;AAAA;AAAA+H,EAAA,GATxB9G,IAAI;AAmpBV,eAAeA,IAAI;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}