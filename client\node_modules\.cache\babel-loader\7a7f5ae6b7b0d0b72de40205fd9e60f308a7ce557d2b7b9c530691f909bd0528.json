{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\ProfilePicture.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  ...props\n}) => {\n  _s();\n  const [imageError, setImageError] = useState(false);\n  const [isOnline, setIsOnline] = useState(false);\n\n  // Simple online status check - assume online if user exists and was recently active\n  useEffect(() => {\n    if (user && showOnlineStatus) {\n      // Simple logic: if user has recent activity (within last 5 minutes), show as online\n      const lastSeen = user.lastSeen ? new Date(user.lastSeen) : null;\n      const now = new Date();\n      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);\n      setIsOnline(lastSeen && lastSeen > fiveMinutesAgo);\n    }\n  }, [user, showOnlineStatus]);\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'xs':\n        return {\n          container: 'w-6 h-6',\n          text: 'text-xs'\n        };\n      case 'sm':\n        return {\n          container: 'w-8 h-8',\n          text: 'text-xs'\n        };\n      case 'md':\n        return {\n          container: 'w-10 h-10',\n          text: 'text-sm'\n        };\n      case 'lg':\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base'\n        };\n      case 'xl':\n        return {\n          container: 'w-16 h-16',\n          text: 'text-lg'\n        };\n      case '2xl':\n        return {\n          container: 'w-20 h-20',\n          text: 'text-xl'\n        };\n      case '3xl':\n        return {\n          container: 'w-24 h-24',\n          text: 'text-2xl'\n        };\n      default:\n        return {\n          container: 'w-10 h-10',\n          text: 'text-sm'\n        };\n    }\n  };\n  const sizeClasses = getSizeClasses();\n  const isClickable = onClick !== null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative inline-block ${className}`,\n    style: {\n      padding: showOnlineStatus ? '2px' : '0'\n    },\n    ...props,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n          ${sizeClasses.container}\n          rounded-full overflow-hidden border-2 border-white/20 relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `,\n      style: {\n        background: '#f0f0f0',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n        ...style\n      },\n      onClick: onClick,\n      children: [!user ?\n      /*#__PURE__*/\n      // Show fallback for undefined user\n      _jsxDEV(\"div\", {\n        className: `\n              rounded-full flex items-center justify-center font-semibold w-full h-full\n              ${sizeClasses.text}\n            `,\n        style: {\n          background: '#25D366',\n          color: '#FFFFFF'\n        },\n        children: \"?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this) : user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.profileImage || user.profilePicture,\n        alt: user.name || 'User',\n        className: \"object-cover rounded-full w-full h-full\",\n        style: {\n          objectFit: 'cover'\n        },\n        onError: e => {\n          // Fallback to initials if image fails to load\n          e.target.style.display = 'none';\n          e.target.nextSibling.style.display = 'flex';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this) : null, user && !(user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n              rounded-full flex items-center justify-center font-semibold w-full h-full\n              ${sizeClasses.text}\n            `,\n        style: {\n          background: '#25D366',\n          color: '#FFFFFF'\n        },\n        children: ((user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'U').charAt(0).toUpperCase()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), showOnlineStatus && (user === null || user === void 0 ? void 0 : user._id) && !loading && isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        width: size === 'xs' ? '8px' : size === 'sm' ? '10px' : size === 'md' ? '12px' : size === 'lg' ? '14px' : size === 'xl' ? '16px' : size === '2xl' ? '18px' : size === '3xl' ? '20px' : '12px',\n        height: size === 'xs' ? '8px' : size === 'sm' ? '10px' : size === 'md' ? '12px' : size === 'lg' ? '14px' : size === 'xl' ? '16px' : size === '2xl' ? '18px' : size === '3xl' ? '20px' : '12px',\n        bottom: '-3px',\n        right: '-3px',\n        zIndex: 999,\n        borderRadius: '50%',\n        backgroundColor: '#22c55e',\n        background: '#22c55e',\n        border: '2px solid #1f2937',\n        outline: 'none',\n        boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.8)',\n        animation: 'pulse 2s infinite'\n      },\n      title: \"Online\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePicture, \"Hc+XpCiTlzDOlhmGPw3PJ26JyUw=\");\n_c = ProfilePicture;\nexport default ProfilePicture;\nvar _c;\n$RefreshReg$(_c, \"ProfilePicture\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ProfilePicture", "user", "size", "showOnlineStatus", "className", "onClick", "style", "props", "_s", "imageError", "setImageError", "isOnline", "setIsOnline", "lastSeen", "Date", "now", "fiveMinutesAgo", "getTime", "getSizeClasses", "container", "text", "sizeClasses", "isClickable", "padding", "children", "background", "boxShadow", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "profileImage", "profilePicture", "src", "alt", "name", "objectFit", "onError", "e", "target", "display", "nextS<PERSON>ling", "username", "char<PERSON>t", "toUpperCase", "_id", "loading", "position", "width", "height", "bottom", "right", "zIndex", "borderRadius", "backgroundColor", "border", "outline", "animation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/ProfilePicture.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  ...props\n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [isOnline, setIsOnline] = useState(false);\n\n  // Simple online status check - assume online if user exists and was recently active\n  useEffect(() => {\n    if (user && showOnlineStatus) {\n      // Simple logic: if user has recent activity (within last 5 minutes), show as online\n      const lastSeen = user.lastSeen ? new Date(user.lastSeen) : null;\n      const now = new Date();\n      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);\n\n      setIsOnline(lastSeen && lastSeen > fiveMinutesAgo);\n    }\n  }, [user, showOnlineStatus]);\n\n  const getSizeClasses = () => {\n    switch (size) {\n      case 'xs':\n        return { container: 'w-6 h-6', text: 'text-xs' };\n      case 'sm':\n        return { container: 'w-8 h-8', text: 'text-xs' };\n      case 'md':\n        return { container: 'w-10 h-10', text: 'text-sm' };\n      case 'lg':\n        return { container: 'w-12 h-12', text: 'text-base' };\n      case 'xl':\n        return { container: 'w-16 h-16', text: 'text-lg' };\n      case '2xl':\n        return { container: 'w-20 h-20', text: 'text-xl' };\n      case '3xl':\n        return { container: 'w-24 h-24', text: 'text-2xl' };\n      default:\n        return { container: 'w-10 h-10', text: 'text-sm' };\n    }\n  };\n\n  const sizeClasses = getSizeClasses();\n  const isClickable = onClick !== null;\n\n  return (\n    <div \n      className={`relative inline-block ${className}`} \n      style={{ padding: showOnlineStatus ? '2px' : '0' }}\n      {...props}\n    >\n      <div\n        className={`\n          ${sizeClasses.container}\n          rounded-full overflow-hidden border-2 border-white/20 relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `}\n        style={{\n          background: '#f0f0f0',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n          ...style\n        }}\n        onClick={onClick}\n      >\n        {!user ? (\n          // Show fallback for undefined user\n          <div\n            className={`\n              rounded-full flex items-center justify-center font-semibold w-full h-full\n              ${sizeClasses.text}\n            `}\n            style={{\n              background: '#25D366',\n              color: '#FFFFFF'\n            }}\n          >\n            ?\n          </div>\n        ) : user?.profileImage || user?.profilePicture ? (\n          <img\n            src={user.profileImage || user.profilePicture}\n            alt={user.name || 'User'}\n            className=\"object-cover rounded-full w-full h-full\"\n            style={{ objectFit: 'cover' }}\n            onError={(e) => {\n              // Fallback to initials if image fails to load\n              e.target.style.display = 'none';\n              e.target.nextSibling.style.display = 'flex';\n            }}\n          />\n        ) : null}\n        \n        {/* Fallback initials - only show if user exists and has no profile image */}\n        {user && !(user?.profileImage || user?.profilePicture) && (\n          <div\n            className={`\n              rounded-full flex items-center justify-center font-semibold w-full h-full\n              ${sizeClasses.text}\n            `}\n            style={{\n              background: '#25D366',\n              color: '#FFFFFF'\n            }}\n          >\n            {(user?.name || user?.username || 'U').charAt(0).toUpperCase()}\n          </div>\n        )}\n      </div>\n\n      {/* Online Status Indicator - Only show if actually online */}\n      {showOnlineStatus && user?._id && !loading && isOnline && (\n        <div\n          style={{\n            position: 'absolute',\n            width: size === 'xs' ? '8px' :\n                   size === 'sm' ? '10px' :\n                   size === 'md' ? '12px' :\n                   size === 'lg' ? '14px' :\n                   size === 'xl' ? '16px' :\n                   size === '2xl' ? '18px' :\n                   size === '3xl' ? '20px' : '12px',\n            height: size === 'xs' ? '8px' :\n                    size === 'sm' ? '10px' :\n                    size === 'md' ? '12px' :\n                    size === 'lg' ? '14px' :\n                    size === 'xl' ? '16px' :\n                    size === '2xl' ? '18px' :\n                    size === '3xl' ? '20px' : '12px',\n            bottom: '-3px',\n            right: '-3px',\n            zIndex: 999,\n            borderRadius: '50%',\n            backgroundColor: '#22c55e',\n            background: '#22c55e',\n            border: '2px solid #1f2937',\n            outline: 'none',\n            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.8)',\n            animation: 'pulse 2s infinite'\n          }}\n          title=\"Online\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProfilePicture;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,cAAc,GAAGA,CAAC;EACtBC,IAAI;EACJC,IAAI,GAAG,IAAI;EACXC,gBAAgB,GAAG,IAAI;EACvBC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,IAAI;EACdC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAC,SAAS,CAAC,MAAM;IACd,IAAII,IAAI,IAAIE,gBAAgB,EAAE;MAC5B;MACA,MAAMU,QAAQ,GAAGZ,IAAI,CAACY,QAAQ,GAAG,IAAIC,IAAI,CAACb,IAAI,CAACY,QAAQ,CAAC,GAAG,IAAI;MAC/D,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAG,IAAIF,IAAI,CAACC,GAAG,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAE9DL,WAAW,CAACC,QAAQ,IAAIA,QAAQ,GAAGG,cAAc,CAAC;IACpD;EACF,CAAC,EAAE,CAACf,IAAI,EAAEE,gBAAgB,CAAC,CAAC;EAE5B,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQhB,IAAI;MACV,KAAK,IAAI;QACP,OAAO;UAAEiB,SAAS,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC;MAClD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAU,CAAC;MAClD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;MACpD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAY,CAAC;MACtD,KAAK,IAAI;QACP,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;MACpD,KAAK,KAAK;QACR,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;MACpD,KAAK,KAAK;QACR,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAW,CAAC;MACrD;QACE,OAAO;UAAED,SAAS,EAAE,WAAW;UAAEC,IAAI,EAAE;QAAU,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,WAAW,GAAGH,cAAc,CAAC,CAAC;EACpC,MAAMI,WAAW,GAAGjB,OAAO,KAAK,IAAI;EAEpC,oBACEN,OAAA;IACEK,SAAS,EAAG,yBAAwBA,SAAU,EAAE;IAChDE,KAAK,EAAE;MAAEiB,OAAO,EAAEpB,gBAAgB,GAAG,KAAK,GAAG;IAAI,CAAE;IAAA,GAC/CI,KAAK;IAAAiB,QAAA,gBAETzB,OAAA;MACEK,SAAS,EAAG;AACpB,YAAYiB,WAAW,CAACF,SAAU;AAClC;AACA,YAAYG,WAAW,GAAG,kEAAkE,GAAG,EAAG;AAClG,SAAU;MACFhB,KAAK,EAAE;QACLmB,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,4BAA4B;QACvC,GAAGpB;MACL,CAAE;MACFD,OAAO,EAAEA,OAAQ;MAAAmB,QAAA,GAEhB,CAACvB,IAAI;MAAA;MACJ;MACAF,OAAA;QACEK,SAAS,EAAG;AACxB;AACA,gBAAgBiB,WAAW,CAACD,IAAK;AACjC,aAAc;QACFd,KAAK,EAAE;UACLmB,UAAU,EAAE,SAAS;UACrBE,KAAK,EAAE;QACT,CAAE;QAAAH,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GACJ9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+B,YAAY,IAAI/B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,cAAc,gBAC5ClC,OAAA;QACEmC,GAAG,EAAEjC,IAAI,CAAC+B,YAAY,IAAI/B,IAAI,CAACgC,cAAe;QAC9CE,GAAG,EAAElC,IAAI,CAACmC,IAAI,IAAI,MAAO;QACzBhC,SAAS,EAAC,yCAAyC;QACnDE,KAAK,EAAE;UAAE+B,SAAS,EAAE;QAAQ,CAAE;QAC9BC,OAAO,EAAGC,CAAC,IAAK;UACd;UACAA,CAAC,CAACC,MAAM,CAAClC,KAAK,CAACmC,OAAO,GAAG,MAAM;UAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACpC,KAAK,CAACmC,OAAO,GAAG,MAAM;QAC7C;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACA,IAAI,EAGP9B,IAAI,IAAI,EAAEA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+B,YAAY,IAAI/B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEgC,cAAc,CAAC,iBACpDlC,OAAA;QACEK,SAAS,EAAG;AACxB;AACA,gBAAgBiB,WAAW,CAACD,IAAK;AACjC,aAAc;QACFd,KAAK,EAAE;UACLmB,UAAU,EAAE,SAAS;UACrBE,KAAK,EAAE;QACT,CAAE;QAAAH,QAAA,EAED,CAAC,CAAAvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmC,IAAI,MAAInC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,QAAQ,KAAI,GAAG,EAAEC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;MAAC;QAAAjB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGL5B,gBAAgB,KAAIF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6C,GAAG,KAAI,CAACC,OAAO,IAAIpC,QAAQ,iBACpDZ,OAAA;MACEO,KAAK,EAAE;QACL0C,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAE/C,IAAI,KAAK,IAAI,GAAG,KAAK,GACrBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,KAAK,GAAG,MAAM,GACvBA,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,MAAM;QACvCgD,MAAM,EAAEhD,IAAI,KAAK,IAAI,GAAG,KAAK,GACrBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,IAAI,GAAG,MAAM,GACtBA,IAAI,KAAK,KAAK,GAAG,MAAM,GACvBA,IAAI,KAAK,KAAK,GAAG,MAAM,GAAG,MAAM;QACxCiD,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,GAAG;QACXC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,SAAS;QAC1B9B,UAAU,EAAE,SAAS;QACrB+B,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,MAAM;QACf/B,SAAS,EAAE,sEAAsE;QACjFgC,SAAS,EAAE;MACb,CAAE;MACFC,KAAK,EAAC;IAAQ;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACvB,EAAA,CAnJIR,cAAc;AAAA4D,EAAA,GAAd5D,cAAc;AAqJpB,eAAeA,cAAc;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}