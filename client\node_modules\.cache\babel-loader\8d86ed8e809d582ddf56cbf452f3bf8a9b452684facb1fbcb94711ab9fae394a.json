{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\common\\\\ProfilePicture.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  ...props\n}) => {\n  _s();\n  const [imageError, setImageError] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [isOnline, setIsOnline] = useState(false);\n\n  // Enhanced online status check\n  useEffect(() => {\n    if (user && showOnlineStatus) {\n      // Check if user was active recently (within last 5 minutes)\n      const lastSeen = user.lastSeen ? new Date(user.lastSeen) : null;\n      const now = new Date();\n      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);\n      setIsOnline(lastSeen && lastSeen > fiveMinutesAgo);\n    }\n  }, [user, showOnlineStatus]);\n  const getSizeConfig = () => {\n    switch (size) {\n      case 'xs':\n        return {\n          container: 'w-6 h-6',\n          text: 'text-xs font-semibold',\n          pixels: 24,\n          onlineSize: 8,\n          border: 'border-2'\n        };\n      case 'sm':\n        return {\n          container: 'w-8 h-8',\n          text: 'text-sm font-semibold',\n          pixels: 32,\n          onlineSize: 10,\n          border: 'border-2'\n        };\n      case 'md':\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n      case 'lg':\n        return {\n          container: 'w-16 h-16',\n          text: 'text-lg font-bold',\n          pixels: 64,\n          onlineSize: 16,\n          border: 'border-3'\n        };\n      case 'xl':\n        return {\n          container: 'w-20 h-20',\n          text: 'text-xl font-bold',\n          pixels: 80,\n          onlineSize: 18,\n          border: 'border-3'\n        };\n      case '2xl':\n        return {\n          container: 'w-24 h-24',\n          text: 'text-2xl font-bold',\n          pixels: 96,\n          onlineSize: 20,\n          border: 'border-4'\n        };\n      case '3xl':\n        return {\n          container: 'w-32 h-32',\n          text: 'text-3xl font-bold',\n          pixels: 128,\n          onlineSize: 24,\n          border: 'border-4'\n        };\n      default:\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n    }\n  };\n  const sizeConfig = getSizeConfig();\n  const isClickable = onClick !== null;\n\n  // Generate user initials\n  const getInitials = user => {\n    if (!user) return '?';\n    const name = user.name || user.username || 'User';\n    const words = name.trim().split(' ');\n    if (words.length >= 2) {\n      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();\n    }\n    return name.charAt(0).toUpperCase();\n  };\n\n  // Generate consistent color based on user name\n  const getAvatarColor = user => {\n    if (!user) return '#6B7280'; // Gray for unknown user\n\n    const name = user.name || user.username || 'User';\n    const colors = ['#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16', '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9', '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF', '#EC4899', '#F43F5E'];\n    let hash = 0;\n    for (let i = 0; i < name.length; i++) {\n      hash = name.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    return colors[Math.abs(hash) % colors.length];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `relative inline-block ${className}`,\n    style: {\n      padding: showOnlineStatus ? '2px' : '0'\n    },\n    ...props,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n          ${sizeConfig.container}\n          rounded-full overflow-hidden ${sizeConfig.border} border-white/20 relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `,\n      style: {\n        background: '#f0f0f0',\n        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n        ...style\n      },\n      onClick: onClick,\n      children: [!user ?\n      /*#__PURE__*/\n      // Show fallback for undefined user\n      _jsxDEV(\"div\", {\n        className: `\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `,\n        style: {\n          background: '#25D366',\n          color: '#FFFFFF'\n        },\n        children: \"?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 11\n      }, this) : user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture ? /*#__PURE__*/_jsxDEV(\"img\", {\n        src: user.profileImage || user.profilePicture,\n        alt: user.name || 'User',\n        className: \"object-cover rounded-full w-full h-full\",\n        style: {\n          objectFit: 'cover'\n        },\n        onError: e => {\n          // Fallback to initials if image fails to load\n          e.target.style.display = 'none';\n          e.target.nextSibling.style.display = 'flex';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this) : null, user && !(user !== null && user !== void 0 && user.profileImage || user !== null && user !== void 0 && user.profilePicture) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `,\n        style: {\n          background: '#25D366',\n          color: '#FFFFFF'\n        },\n        children: ((user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username) || 'U').charAt(0).toUpperCase()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), showOnlineStatus && (user === null || user === void 0 ? void 0 : user._id) && isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        width: `${sizeConfig.onlineSize}px`,\n        height: `${sizeConfig.onlineSize}px`,\n        bottom: '-3px',\n        right: '-3px',\n        zIndex: 999,\n        borderRadius: '50%',\n        backgroundColor: '#22c55e',\n        background: '#22c55e',\n        border: '2px solid #1f2937',\n        outline: 'none',\n        boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.8)',\n        animation: 'pulse 2s infinite'\n      },\n      title: \"Online\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(ProfilePicture, \"d4ExUnGHEMBYMAj71eU+xkTqxug=\");\n_c = ProfilePicture;\nexport default ProfilePicture;\nvar _c;\n$RefreshReg$(_c, \"ProfilePicture\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ProfilePicture", "user", "size", "showOnlineStatus", "className", "onClick", "style", "props", "_s", "imageError", "setImageError", "imageLoaded", "setImageLoaded", "isOnline", "setIsOnline", "lastSeen", "Date", "now", "fiveMinutesAgo", "getTime", "getSizeConfig", "container", "text", "pixels", "onlineSize", "border", "sizeConfig", "isClickable", "getInitials", "name", "username", "words", "trim", "split", "length", "char<PERSON>t", "toUpperCase", "getAvatarColor", "colors", "hash", "i", "charCodeAt", "Math", "abs", "padding", "children", "background", "boxShadow", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "profileImage", "profilePicture", "src", "alt", "objectFit", "onError", "e", "target", "display", "nextS<PERSON>ling", "_id", "position", "width", "height", "bottom", "right", "zIndex", "borderRadius", "backgroundColor", "outline", "animation", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/common/ProfilePicture.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst ProfilePicture = ({\n  user,\n  size = 'md',\n  showOnlineStatus = true,\n  className = '',\n  onClick = null,\n  style = {},\n  ...props\n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [isOnline, setIsOnline] = useState(false);\n\n  // Enhanced online status check\n  useEffect(() => {\n    if (user && showOnlineStatus) {\n      // Check if user was active recently (within last 5 minutes)\n      const lastSeen = user.lastSeen ? new Date(user.lastSeen) : null;\n      const now = new Date();\n      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);\n\n      setIsOnline(lastSeen && lastSeen > fiveMinutesAgo);\n    }\n  }, [user, showOnlineStatus]);\n\n  const getSizeConfig = () => {\n    switch (size) {\n      case 'xs':\n        return {\n          container: 'w-6 h-6',\n          text: 'text-xs font-semibold',\n          pixels: 24,\n          onlineSize: 8,\n          border: 'border-2'\n        };\n      case 'sm':\n        return {\n          container: 'w-8 h-8',\n          text: 'text-sm font-semibold',\n          pixels: 32,\n          onlineSize: 10,\n          border: 'border-2'\n        };\n      case 'md':\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n      case 'lg':\n        return {\n          container: 'w-16 h-16',\n          text: 'text-lg font-bold',\n          pixels: 64,\n          onlineSize: 16,\n          border: 'border-3'\n        };\n      case 'xl':\n        return {\n          container: 'w-20 h-20',\n          text: 'text-xl font-bold',\n          pixels: 80,\n          onlineSize: 18,\n          border: 'border-3'\n        };\n      case '2xl':\n        return {\n          container: 'w-24 h-24',\n          text: 'text-2xl font-bold',\n          pixels: 96,\n          onlineSize: 20,\n          border: 'border-4'\n        };\n      case '3xl':\n        return {\n          container: 'w-32 h-32',\n          text: 'text-3xl font-bold',\n          pixels: 128,\n          onlineSize: 24,\n          border: 'border-4'\n        };\n      default:\n        return {\n          container: 'w-12 h-12',\n          text: 'text-base font-bold',\n          pixels: 48,\n          onlineSize: 12,\n          border: 'border-2'\n        };\n    }\n  };\n\n  const sizeConfig = getSizeConfig();\n  const isClickable = onClick !== null;\n\n  // Generate user initials\n  const getInitials = (user) => {\n    if (!user) return '?';\n    const name = user.name || user.username || 'User';\n    const words = name.trim().split(' ');\n    if (words.length >= 2) {\n      return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();\n    }\n    return name.charAt(0).toUpperCase();\n  };\n\n  // Generate consistent color based on user name\n  const getAvatarColor = (user) => {\n    if (!user) return '#6B7280'; // Gray for unknown user\n\n    const name = user.name || user.username || 'User';\n    const colors = [\n      '#EF4444', '#F97316', '#F59E0B', '#EAB308', '#84CC16',\n      '#22C55E', '#10B981', '#14B8A6', '#06B6D4', '#0EA5E9',\n      '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7', '#D946EF',\n      '#EC4899', '#F43F5E'\n    ];\n\n    let hash = 0;\n    for (let i = 0; i < name.length; i++) {\n      hash = name.charCodeAt(i) + ((hash << 5) - hash);\n    }\n\n    return colors[Math.abs(hash) % colors.length];\n  };\n\n  return (\n    <div \n      className={`relative inline-block ${className}`} \n      style={{ padding: showOnlineStatus ? '2px' : '0' }}\n      {...props}\n    >\n      <div\n        className={`\n          ${sizeConfig.container}\n          rounded-full overflow-hidden ${sizeConfig.border} border-white/20 relative\n          ${isClickable ? 'cursor-pointer hover:scale-105 transition-transform duration-200' : ''}\n        `}\n        style={{\n          background: '#f0f0f0',\n          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',\n          ...style\n        }}\n        onClick={onClick}\n      >\n        {!user ? (\n          // Show fallback for undefined user\n          <div\n            className={`\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `}\n            style={{\n              background: '#25D366',\n              color: '#FFFFFF'\n            }}\n          >\n            ?\n          </div>\n        ) : user?.profileImage || user?.profilePicture ? (\n          <img\n            src={user.profileImage || user.profilePicture}\n            alt={user.name || 'User'}\n            className=\"object-cover rounded-full w-full h-full\"\n            style={{ objectFit: 'cover' }}\n            onError={(e) => {\n              // Fallback to initials if image fails to load\n              e.target.style.display = 'none';\n              e.target.nextSibling.style.display = 'flex';\n            }}\n          />\n        ) : null}\n        \n        {/* Fallback initials - only show if user exists and has no profile image */}\n        {user && !(user?.profileImage || user?.profilePicture) && (\n          <div\n            className={`\n              rounded-full flex items-center justify-center w-full h-full\n              ${sizeConfig.text}\n            `}\n            style={{\n              background: '#25D366',\n              color: '#FFFFFF'\n            }}\n          >\n            {(user?.name || user?.username || 'U').charAt(0).toUpperCase()}\n          </div>\n        )}\n      </div>\n\n      {/* Online Status Indicator - Only show if actually online */}\n      {showOnlineStatus && user?._id && isOnline && (\n        <div\n          style={{\n            position: 'absolute',\n            width: `${sizeConfig.onlineSize}px`,\n            height: `${sizeConfig.onlineSize}px`,\n            bottom: '-3px',\n            right: '-3px',\n            zIndex: 999,\n            borderRadius: '50%',\n            backgroundColor: '#22c55e',\n            background: '#22c55e',\n            border: '2px solid #1f2937',\n            outline: 'none',\n            boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.8)',\n            animation: 'pulse 2s infinite'\n          }}\n          title=\"Online\"\n        />\n      )}\n    </div>\n  );\n};\n\nexport default ProfilePicture;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,cAAc,GAAGA,CAAC;EACtBC,IAAI;EACJC,IAAI,GAAG,IAAI;EACXC,gBAAgB,GAAG,IAAI;EACvBC,SAAS,GAAG,EAAE;EACdC,OAAO,GAAG,IAAI;EACdC,KAAK,GAAG,CAAC,CAAC;EACV,GAAGC;AACL,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACe,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAC,SAAS,CAAC,MAAM;IACd,IAAII,IAAI,IAAIE,gBAAgB,EAAE;MAC5B;MACA,MAAMY,QAAQ,GAAGd,IAAI,CAACc,QAAQ,GAAG,IAAIC,IAAI,CAACf,IAAI,CAACc,QAAQ,CAAC,GAAG,IAAI;MAC/D,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;MACtB,MAAME,cAAc,GAAG,IAAIF,IAAI,CAACC,GAAG,CAACE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;MAE9DL,WAAW,CAACC,QAAQ,IAAIA,QAAQ,GAAGG,cAAc,CAAC;IACpD;EACF,CAAC,EAAE,CAACjB,IAAI,EAAEE,gBAAgB,CAAC,CAAC;EAE5B,MAAMiB,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQlB,IAAI;MACV,KAAK,IAAI;QACP,OAAO;UACLmB,SAAS,EAAE,SAAS;UACpBC,IAAI,EAAE,uBAAuB;UAC7BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,CAAC;UACbC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,SAAS;UACpBC,IAAI,EAAE,uBAAuB;UAC7BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,qBAAqB;UAC3BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,mBAAmB;UACzBC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,IAAI;QACP,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,mBAAmB;UACzBC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,KAAK;QACR,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,oBAAoB;UAC1BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH,KAAK,KAAK;QACR,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,oBAAoB;UAC1BC,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;MACH;QACE,OAAO;UACLJ,SAAS,EAAE,WAAW;UACtBC,IAAI,EAAE,qBAAqB;UAC3BC,MAAM,EAAE,EAAE;UACVC,UAAU,EAAE,EAAE;UACdC,MAAM,EAAE;QACV,CAAC;IACL;EACF,CAAC;EAED,MAAMC,UAAU,GAAGN,aAAa,CAAC,CAAC;EAClC,MAAMO,WAAW,GAAGtB,OAAO,KAAK,IAAI;;EAEpC;EACA,MAAMuB,WAAW,GAAI3B,IAAI,IAAK;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,GAAG;IACrB,MAAM4B,IAAI,GAAG5B,IAAI,CAAC4B,IAAI,IAAI5B,IAAI,CAAC6B,QAAQ,IAAI,MAAM;IACjD,MAAMC,KAAK,GAAGF,IAAI,CAACG,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIF,KAAK,CAACG,MAAM,IAAI,CAAC,EAAE;MACrB,OAAO,CAACH,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,GAAGJ,KAAK,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC;IAChE;IACA,OAAOP,IAAI,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAMC,cAAc,GAAIpC,IAAI,IAAK;IAC/B,IAAI,CAACA,IAAI,EAAE,OAAO,SAAS,CAAC,CAAC;;IAE7B,MAAM4B,IAAI,GAAG5B,IAAI,CAAC4B,IAAI,IAAI5B,IAAI,CAAC6B,QAAQ,IAAI,MAAM;IACjD,MAAMQ,MAAM,GAAG,CACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,CACrB;IAED,IAAIC,IAAI,GAAG,CAAC;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,IAAI,CAACK,MAAM,EAAEM,CAAC,EAAE,EAAE;MACpCD,IAAI,GAAGV,IAAI,CAACY,UAAU,CAACD,CAAC,CAAC,IAAI,CAACD,IAAI,IAAI,CAAC,IAAIA,IAAI,CAAC;IAClD;IAEA,OAAOD,MAAM,CAACI,IAAI,CAACC,GAAG,CAACJ,IAAI,CAAC,GAAGD,MAAM,CAACJ,MAAM,CAAC;EAC/C,CAAC;EAED,oBACEnC,OAAA;IACEK,SAAS,EAAG,yBAAwBA,SAAU,EAAE;IAChDE,KAAK,EAAE;MAAEsC,OAAO,EAAEzC,gBAAgB,GAAG,KAAK,GAAG;IAAI,CAAE;IAAA,GAC/CI,KAAK;IAAAsC,QAAA,gBAET9C,OAAA;MACEK,SAAS,EAAG;AACpB,YAAYsB,UAAU,CAACL,SAAU;AACjC,yCAAyCK,UAAU,CAACD,MAAO;AAC3D,YAAYE,WAAW,GAAG,kEAAkE,GAAG,EAAG;AAClG,SAAU;MACFrB,KAAK,EAAE;QACLwC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,4BAA4B;QACvC,GAAGzC;MACL,CAAE;MACFD,OAAO,EAAEA,OAAQ;MAAAwC,QAAA,GAEhB,CAAC5C,IAAI;MAAA;MACJ;MACAF,OAAA;QACEK,SAAS,EAAG;AACxB;AACA,gBAAgBsB,UAAU,CAACJ,IAAK;AAChC,aAAc;QACFhB,KAAK,EAAE;UACLwC,UAAU,EAAE,SAAS;UACrBE,KAAK,EAAE;QACT,CAAE;QAAAH,QAAA,EACH;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GACJnD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoD,YAAY,IAAIpD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqD,cAAc,gBAC5CvD,OAAA;QACEwD,GAAG,EAAEtD,IAAI,CAACoD,YAAY,IAAIpD,IAAI,CAACqD,cAAe;QAC9CE,GAAG,EAAEvD,IAAI,CAAC4B,IAAI,IAAI,MAAO;QACzBzB,SAAS,EAAC,yCAAyC;QACnDE,KAAK,EAAE;UAAEmD,SAAS,EAAE;QAAQ,CAAE;QAC9BC,OAAO,EAAGC,CAAC,IAAK;UACd;UACAA,CAAC,CAACC,MAAM,CAACtD,KAAK,CAACuD,OAAO,GAAG,MAAM;UAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAACxD,KAAK,CAACuD,OAAO,GAAG,MAAM;QAC7C;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACA,IAAI,EAGPnD,IAAI,IAAI,EAAEA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoD,YAAY,IAAIpD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqD,cAAc,CAAC,iBACpDvD,OAAA;QACEK,SAAS,EAAG;AACxB;AACA,gBAAgBsB,UAAU,CAACJ,IAAK;AAChC,aAAc;QACFhB,KAAK,EAAE;UACLwC,UAAU,EAAE,SAAS;UACrBE,KAAK,EAAE;QACT,CAAE;QAAAH,QAAA,EAED,CAAC,CAAA5C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4B,IAAI,MAAI5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,QAAQ,KAAI,GAAG,EAAEK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;MAAC;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLjD,gBAAgB,KAAIF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,GAAG,KAAIlD,QAAQ,iBACxCd,OAAA;MACEO,KAAK,EAAE;QACL0D,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAG,GAAEvC,UAAU,CAACF,UAAW,IAAG;QACnC0C,MAAM,EAAG,GAAExC,UAAU,CAACF,UAAW,IAAG;QACpC2C,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,GAAG;QACXC,YAAY,EAAE,KAAK;QACnBC,eAAe,EAAE,SAAS;QAC1BzB,UAAU,EAAE,SAAS;QACrBrB,MAAM,EAAE,mBAAmB;QAC3B+C,OAAO,EAAE,MAAM;QACfzB,SAAS,EAAE,sEAAsE;QACjF0B,SAAS,EAAE;MACb,CAAE;MACFC,KAAK,EAAC;IAAQ;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAvNIR,cAAc;AAAA2E,EAAA,GAAd3E,cAAc;AAyNpB,eAAeA,cAAc;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}