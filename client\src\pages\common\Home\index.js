import React, { useState, useRef } from "react";
import "./index.css";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowBigRightLinesFilled,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbStar,
  TbSchool
} from "react-icons/tb";
import { message } from "antd";
import { useSelector } from "react-redux";
import Image1 from "../../../assets/collage-1.png";
import { contactUs } from "../../../apicalls/users";
import NotificationBell from "../../../components/common/NotificationBell";
import ProfilePicture from "../../../components/common/ProfilePicture";

const Home = () => {
  const homeSectionRef = useRef(null);
  const reviewsSectionRef = useRef(null);
  const contactUsRef = useRef(null);
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const [menuOpen, setMenuOpen] = useState(false);
  const { user } = useSelector((state) => state.user);

  // Check if user is logged in
  const isLoggedIn = !!user;



  const scrollToSection = (ref, offset = 80) => {
    if (ref?.current) {
      const sectionTop = ref.current.offsetTop;
      window.scrollTo({ top: sectionTop - offset, behavior: "smooth" });
      // Close mobile menu after clicking
      setMenuOpen(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponseMessage("");
    try {
      const data = await contactUs(formData);
      if (data.success) {
        message.success("Message sent successfully!");
        setResponseMessage("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" });
      } else {
        setResponseMessage(data.message || "Something went wrong.");
      }
    } catch (error) {
      setResponseMessage("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  return (
    <div className="Home">
      {/* Navigation */}
      <motion.nav
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-header fixed w-full top-0 z-50"
      >
        <div className="container">
          <div className="flex items-center justify-between h-16">
            {/* Left Section - Navigation */}
            <div className="hidden md:flex items-center space-x-6">
              <button onClick={() => scrollToSection(homeSectionRef)} className="nav-item">Home</button>
              <button onClick={() => scrollToSection(reviewsSectionRef)} className="nav-item">Reviews</button>
              <button onClick={() => scrollToSection(contactUsRef)} className="nav-item">Contact Us</button>
            </div>

            {/* Center Section - Logo */}
            <div className="flex-1 flex justify-center">
              <Link to="/" className="flex items-center space-x-2 sm:space-x-3">
                {/* Tanzania Flag */}
                <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white">
                  <div className="w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative">
                    <div className="absolute inset-0 bg-black/20"></div>
                    <div className="absolute top-0 left-0 w-full h-1/3 bg-green-500"></div>
                    <div className="absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400"></div>
                    <div className="absolute bottom-0 left-0 w-full h-1/3 bg-blue-500"></div>
                  </div>
                </div>

                {/* Amazing Animated Brainwave Text */}
                <div className="relative brainwave-container">
                  <h1 className="text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none"
                      style={{
                        fontFamily: "'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif",
                        letterSpacing: '-0.02em'
                      }}>
                    {/* Brain - with amazing effects */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: -30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        textShadow: [
                          "0 0 10px rgba(59, 130, 246, 0.5)",
                          "0 0 20px rgba(59, 130, 246, 0.8)",
                          "0 0 10px rgba(59, 130, 246, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.3,
                        textShadow: {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, -2, 2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#1f2937',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
                      }}
                    >
                      Brain

                      {/* Electric spark */}
                      <motion.div
                        className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          scale: [0.5, 1.2, 0.5],
                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          delay: 2
                        }}
                        style={{
                          backgroundColor: '#3b82f6',
                          boxShadow: '0 0 10px #3b82f6'
                        }}
                      />
                    </motion.span>

                    {/* Wave - with flowing effects */}
                    <motion.span
                      className="relative inline-block ml-1"
                      initial={{ opacity: 0, x: 30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        textShadow: [
                          "0 0 10px rgba(16, 185, 129, 0.5)",
                          "0 0 20px rgba(16, 185, 129, 0.8)",
                          "0 0 10px rgba(16, 185, 129, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.6,
                        textShadow: {
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut",
                          delay: 0.5
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, 2, -2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#10b981',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
                      }}
                    >
                      wave

                      {/* Wave particles */}
                      <motion.div
                        className="absolute -top-1 -left-1 w-1 h-1 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          scale: [0.5, 1, 0.5],
                          x: [0, 10, 0],
                          backgroundColor: ['#10b981', '#34d399', '#10b981']
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          delay: 3
                        }}
                        style={{
                          backgroundColor: '#10b981',
                          boxShadow: '0 0 8px #10b981'
                        }}
                      />
                    </motion.span>
                  </h1>

                  {/* Glowing underline effect */}
                  <motion.div
                    className="absolute -bottom-1 left-0 h-1 rounded-full"
                    initial={{ width: 0, opacity: 0 }}
                    animate={{
                      width: '100%',
                      opacity: 1,
                      boxShadow: [
                        '0 0 10px rgba(16, 185, 129, 0.5)',
                        '0 0 20px rgba(59, 130, 246, 0.8)',
                        '0 0 10px rgba(16, 185, 129, 0.5)'
                      ]
                    }}
                    transition={{
                      duration: 1.5,
                      delay: 1.2,
                      boxShadow: {
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                    style={{
                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',
                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                    }}
                  />
                </div>

                {/* Official Logo */}
                <div className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white">
                  <img
                    src="/favicon.png"
                    alt="Brainwave Logo"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                  <div className="w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm sm:text-base md:text-lg" style={{display: 'none'}}>
                    🧠
                  </div>
                </div>
              </Link>
            </div>

            {/* Right Section - Only Notifications and Profile */}
            <div className="flex items-center space-x-3 sm:space-x-4">
              {/* Notification Bell - Only show if logged in and not admin */}
              {isLoggedIn && !user?.isAdmin && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <NotificationBell />
                </motion.div>
              )}

              {/* User Profile - Ranking Style and Responsive */}
              {isLoggedIn && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-center space-x-2 sm:space-x-3 group cursor-pointer"
                >
                  {/* Profile Picture - Ranking Style */}
                  <div className="relative">
                    <ProfilePicture
                      user={user}
                      size="sm"
                      showOnlineStatus={true}
                      className="ring-2 ring-blue-200 hover:ring-blue-300 transition-all duration-300"
                      style={{
                        width: '36px',
                        height: '36px',
                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'
                      }}
                    />
                  </div>

                  {/* User Details - Responsive */}
                  <div className="hidden sm:block text-right">
                    <div className="text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 truncate max-w-[120px] lg:max-w-[160px]">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300">
                      {user?.isAdmin ? 'Administrator' : `Class ${user?.class || 'N/A'}`}
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>

          {/* Mobile Navigation */}
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="md:hidden mobile-nav"
            >
              <div className="flex flex-col">
                <button onClick={() => scrollToSection(homeSectionRef)} className="nav-item">Home</button>
                <button onClick={() => scrollToSection(reviewsSectionRef)} className="nav-item">Reviews</button>
                <button onClick={() => scrollToSection(contactUsRef)} className="nav-item">Contact Us</button>

                {/* Mobile Login/Register - Only show if not logged in */}
                {!isLoggedIn && (
                  <div className="flex flex-col space-y-2 mt-4 pt-4 border-t border-gray-200">
                    <Link
                      to="/login"
                      className="nav-item text-center"
                      onClick={() => setMenuOpen(false)}
                    >
                      Login
                    </Link>
                    <Link
                      to="/register"
                      className="nav-item text-center bg-blue-600 text-white rounded-lg"
                      onClick={() => setMenuOpen(false)}
                    >
                      Register
                    </Link>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section ref={homeSectionRef} className="hero-section">
        <div className="container">
          <div className="hero-grid">
            {/* Hero Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="hero-content"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="hero-badge"
              >
                <TbSchool className="w-5 h-5 mr-2" />
                #1 Educational Platform in Tanzania
              </motion.div>

              <h1 className="hero-title">
                Fueling Bright Futures with{" "}
                <span className="text-gradient">
                  Education
                  <TbArrowBigRightLinesFilled className="inline w-8 h-8 ml-2" />
                </span>
              </h1>

              <p className="hero-subtitle">
                Discover limitless learning opportunities with our comprehensive
                online study platform. Study anywhere, anytime, and achieve your
                academic goals with confidence.
              </p>

              {/* Explore Platform Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="cta-section"
              >
                <Link to="/login">
                  <button className="btn btn-primary btn-large">
                    <TbBrain className="w-5 h-5 mr-2" />
                    Explore Platform
                  </button>
                </Link>
              </motion.div>

              {/* Trust Indicators */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="trust-indicators"
              >
                <div className="trust-indicator">
                  <TbUsers style={{color: '#007BFF'}} />
                  <span>15K+ Students</span>
                </div>
                <div className="trust-indicator">
                  <TbStar style={{color: '#f59e0b'}} />
                  <span>4.9/5 Rating</span>
                </div>
                <div className="trust-indicator">
                  <TbTrophy style={{color: '#007BFF'}} />
                  <span>Award Winning</span>
                </div>
              </motion.div>
            </motion.div>

            {/* Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="hero-image"
            >
              <div className="relative">
                <img
                  src={Image1}
                  alt="Students Learning"
                  loading="lazy"
                />

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="floating-element"
                  style={{top: '-1rem', left: '-1rem'}}
                >
                  <TbBook style={{color: '#007BFF'}} />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="floating-element"
                  style={{bottom: '-1rem', right: '-1rem'}}
                >
                  <TbTrophy style={{color: '#f59e0b'}} />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="stats-grid"
          >
            {[
              { number: "15K+", text: "Active Students" },
              { number: "500+", text: "Expert Teachers" },
              { number: "1000+", text: "Video Lessons" },
              { number: "98%", text: "Success Rate" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="stat-item"
              >
                <div className="stat-number">{stat.number}</div>
                <p className="stat-text">{stat.text}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Reviews Section */}
      <section ref={reviewsSectionRef} className="reviews-section">
        <div className="reviews-container">
          <h2 className="reviews-title">
            Reviews from our students
          </h2>
          <div className="reviews-grid">
            {[
              {
                rating: 5,
                text: "BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.",
                user: { name: "Sarah Johnson" }
              },
              {
                rating: 5,
                text: "The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.",
                user: { name: "Michael Chen" }
              },
              {
                rating: 5,
                text: "Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!",
                user: { name: "Amina Hassan" }
              }
            ].map((review, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="review-card"
              >
                <div className="review-rating">
                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>
                    {'★'.repeat(review.rating)}
                  </div>
                </div>
                <div className="review-text">"{review.text}"</div>
                <div className="review-divider"></div>
                <div className="review-author">{review.user?.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section ref={contactUsRef} className="contact-section">
        <div className="contact-container">
          <h2 className="contact-title">Contact Us</h2>
          <p className="contact-subtitle">Get in touch with us for any questions or support</p>
          <form className="contact-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label className="form-label">Name</label>
              <input
                type="text"
                name="name"
                placeholder="Your Name"
                className="form-input"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label className="form-label">Email</label>
              <input
                type="email"
                name="email"
                placeholder="Your Email"
                className="form-input"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label className="form-label">Message</label>
              <textarea
                name="message"
                placeholder="Your Message"
                className="form-input form-textarea"
                value={formData.message}
                onChange={handleChange}
                required
              ></textarea>
            </div>
            <button
              type="submit"
              className="form-submit"
              disabled={loading}
            >
              {loading ? "Sending..." : "Send Message"}
            </button>
            {responseMessage && (
              <p className="response-message" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>
                {responseMessage}
              </p>
            )}
          </form>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <p className="footer-text">
            © 2024 BrainWave Educational Platform. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Home;
