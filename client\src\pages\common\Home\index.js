import React, { useState, useRef } from "react";
import "./index.css";
import { Link, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import {
  TbArrowBigRightLinesFilled,
  TbBrain,
  TbBook,
  TbTrophy,
  TbUsers,
  TbStar,
  TbSchool
} from "react-icons/tb";
import { message } from "antd";
import { useSelector } from "react-redux";
import Image1 from "../../../assets/collage-1.png";
import { contactUs } from "../../../apicalls/users";
import NotificationBell from "../../../components/common/NotificationBell";
import ProfilePicture from "../../../components/common/ProfilePicture";

const Home = () => {
  const homeSectionRef = useRef(null);
  const reviewsSectionRef = useRef(null);
  const contactUsRef = useRef(null);
  const [formData, setFormData] = useState({ name: "", email: "", message: "" });
  const [loading, setLoading] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const [menuOpen, setMenuOpen] = useState(false);
  const { user } = useSelector((state) => state.user);
  const location = useLocation();

  // Check if user is logged in
  const isLoggedIn = !!user;



  const scrollToSection = (ref, offset = 80) => {
    if (ref?.current) {
      const sectionTop = ref.current.offsetTop;
      window.scrollTo({ top: sectionTop - offset, behavior: "smooth" });
      // Close mobile menu after clicking
      setMenuOpen(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponseMessage("");
    try {
      const data = await contactUs(formData);
      if (data.success) {
        message.success("Message sent successfully!");
        setResponseMessage("Message sent successfully!");
        setFormData({ name: "", email: "", message: "" });
      } else {
        setResponseMessage(data.message || "Something went wrong.");
      }
    } catch (error) {
      setResponseMessage("Error sending message. Please try again.");
    }
    setLoading(false);
  };

  return (
    <div className="Home">
      {/* Modern Responsive Header - Same as ProtectedRoute */}
      <motion.header
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        className="nav-modern bg-gradient-to-r from-white/98 via-blue-50/95 to-white/98 backdrop-blur-xl border-b border-blue-100/50 sticky top-0 z-30 shadow-lg shadow-blue-100/20"
      >
        <div className="px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 xl:px-10">
          <div className="flex items-center justify-between h-12 xs:h-14 sm:h-16 md:h-18 lg:h-20">
            {/* Left section - Trust Indicators */}
            <div className="flex items-center space-x-4">
              <div className="hidden lg:flex items-center space-x-6">
                <div className="flex items-center space-x-2 text-sm">
                  <TbUsers className="w-4 h-4 text-blue-600" />
                  <span className="font-semibold text-gray-700">15K+ Students</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <TbStar className="w-4 h-4 text-yellow-500" />
                  <span className="font-semibold text-gray-700">4.9/5 Rating</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <TbTrophy className="w-4 h-4 text-blue-600" />
                  <span className="font-semibold text-gray-700">Award Winning</span>
                </div>
              </div>
              {/* Mobile version - condensed */}
              <div className="flex lg:hidden items-center space-x-3">
                <div className="flex items-center space-x-1 text-xs">
                  <TbUsers className="w-3 h-3 text-blue-600" />
                  <span className="font-semibold text-gray-700">15K+</span>
                </div>
                <div className="flex items-center space-x-1 text-xs">
                  <TbStar className="w-3 h-3 text-yellow-500" />
                  <span className="font-semibold text-gray-700">4.9/5</span>
                </div>
              </div>
            </div>

            {/* Center Section - Tanzania Flag + Brainwave Title + Logo */}
            <div className="flex-1 flex justify-center">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="relative group flex items-center space-x-3"
              >
                {/* Tanzania Flag - Using actual flag image */}
                <div
                  className="rounded-md overflow-hidden border-2 border-gray-300 shadow-lg relative"
                  style={{
                    width: '32px',
                    height: '24px'
                  }}
                >
                  <img
                    src="https://flagcdn.com/w40/tz.png"
                    alt="Tanzania Flag"
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      // Fallback to another flag source if first fails
                      e.target.src = "https://upload.wikimedia.org/wikipedia/commons/thumb/3/38/Flag_of_Tanzania.svg/32px-Flag_of_Tanzania.svg.png";
                      e.target.onerror = () => {
                        // Final fallback - hide image and show text
                        e.target.style.display = 'none';
                        e.target.parentElement.innerHTML = '<div class="w-full h-full bg-blue-600 flex items-center justify-center text-white text-xs font-bold">TZ</div>';
                      };
                    }}
                  />
                </div>

                {/* Amazing Animated Brainwave Text */}
                <div className="relative brainwave-container">
                  <h1 className="text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none"
                      style={{
                        fontFamily: "'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif",
                        letterSpacing: '-0.02em'
                      }}>
                    {/* Brain - with amazing effects */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: -30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        textShadow: [
                          "0 0 10px rgba(59, 130, 246, 0.5)",
                          "0 0 20px rgba(59, 130, 246, 0.8)",
                          "0 0 10px rgba(59, 130, 246, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.3,
                        textShadow: {
                          duration: 2,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, -2, 2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#1f2937',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
                      }}
                    >
                      Brain

                      {/* Electric spark */}
                      <motion.div
                        className="absolute -top-1 -right-1 w-2 h-2 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          scale: [0.5, 1.2, 0.5],
                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']
                        }}
                        transition={{
                          duration: 1.5,
                          repeat: Infinity,
                          delay: 2
                        }}
                        style={{
                          backgroundColor: '#3b82f6',
                          boxShadow: '0 0 10px #3b82f6'
                        }}
                      />
                    </motion.span>

                    {/* Wave - with flowing effects (no space) */}
                    <motion.span
                      className="relative inline-block"
                      initial={{ opacity: 0, x: 30, scale: 0.8 }}
                      animate={{
                        opacity: 1,
                        x: 0,
                        scale: 1,
                        y: [0, -2, 0, 2, 0],
                        textShadow: [
                          "0 0 10px rgba(16, 185, 129, 0.5)",
                          "0 0 20px rgba(16, 185, 129, 0.8)",
                          "0 0 10px rgba(16, 185, 129, 0.5)"
                        ]
                      }}
                      transition={{
                        duration: 1,
                        delay: 0.5,
                        y: {
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        },
                        textShadow: {
                          duration: 2.5,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }
                      }}
                      whileHover={{
                        scale: 1.1,
                        rotate: [0, 2, -2, 0],
                        transition: { duration: 0.3 }
                      }}
                      style={{
                        color: '#059669',
                        fontWeight: '900',
                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'
                      }}
                    >
                      wave

                      {/* Wave particle */}
                      <motion.div
                        className="absolute top-0 left-0 w-1.5 h-1.5 rounded-full"
                        animate={{
                          opacity: [0, 1, 0],
                          x: [0, 40, 80],
                          y: [0, -5, 0, 5, 0],
                          backgroundColor: ['#10b981', '#34d399', '#10b981']
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: 1
                        }}
                        style={{
                          backgroundColor: '#10b981',
                          boxShadow: '0 0 8px #10b981'
                        }}
                      />
                    </motion.span>
                  </h1>

                  {/* Glowing underline effect */}
                  <motion.div
                    className="absolute -bottom-1 left-0 h-1 rounded-full"
                    initial={{ width: 0, opacity: 0 }}
                    animate={{
                      width: '100%',
                      opacity: 1,
                      boxShadow: [
                        '0 0 10px rgba(16, 185, 129, 0.5)',
                        '0 0 20px rgba(59, 130, 246, 0.8)',
                        '0 0 10px rgba(16, 185, 129, 0.5)'
                      ]
                    }}
                    transition={{
                      duration: 1.5,
                      delay: 1.2,
                      boxShadow: {
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                    style={{
                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',
                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'
                    }}
                  />
                </div>

                {/* Official Logo - Small like profile */}
                <div
                  className="rounded-full overflow-hidden border-2 border-white/20 relative"
                  style={{
                    background: '#f0f0f0',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                    width: '32px',
                    height: '32px'
                  }}
                >
                  <img
                    src="/favicon.png"
                    alt="Brainwave Logo"
                    className="w-full h-full object-cover"
                    style={{ objectFit: 'cover' }}
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'flex';
                    }}
                  />
                  <div
                    className="w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold"
                    style={{
                      display: 'none',
                      fontSize: '12px'
                    }}
                  >
                    🧠
                  </div>
                </div>

                {/* Modern Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-gray-900/5 to-blue-500/5 blur-xl opacity-0 group-hover:opacity-100 transition-all duration-700 -z-10 scale-110"></div>
              </motion.div>
            </div>

            {/* Right Section - Contact Us + Auth Buttons */}
            <div className="flex items-center justify-end space-x-3 sm:space-x-4">
              {/* Contact Us Button */}
              <button
                onClick={() => scrollToSection(contactUsRef)}
                className="hidden md:block nav-item text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-300"
              >
                Contact Us
              </button>

              {/* User Profile Section - if logged in */}
              {user ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="flex items-center space-x-2 group"
                >
                  {/* Notification Bell */}
                  {!user?.isAdmin && (
                    <NotificationBell />
                  )}

                  {/* Profile Picture with Online Status */}
                  <ProfilePicture
                    user={user}
                    size="sm"
                    showOnlineStatus={true}
                    style={{
                      width: '32px',
                      height: '32px'
                    }}
                  />

                  {/* User Name and Class */}
                  <div className="hidden sm:block text-right">
                    <div className="text-xs md:text-sm font-medium text-gray-700 group-hover:text-green-600 transition-colors duration-300">
                      {user?.name || 'User'}
                    </div>
                    <div className="text-xs text-gray-500 group-hover:text-green-500 transition-colors duration-300">
                      Class {user?.class}
                    </div>
                  </div>
                </motion.div>
              ) : (
                /* Login and Registration Buttons - if not logged in */
                <div className="flex items-center space-x-2">
                  <Link
                    to="/login"
                    className="px-3 py-1.5 text-sm font-medium text-blue-600 hover:text-blue-700 border border-blue-600 hover:border-blue-700 rounded-lg transition-all duration-300"
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="px-3 py-1.5 text-sm font-medium bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg"
                  >
                    Registration
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.header>

      {/* Hero Section */}
      <section ref={homeSectionRef} className="hero-section">
        <div className="container">
          <div className="hero-grid">
            {/* Hero Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="hero-content"
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="hero-badge"
              >
                <TbSchool className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                <span className="text-xs sm:text-sm">#1 Educational Platform in Tanzania</span>
              </motion.div>

              <h1 className="hero-title">
                Fueling Bright Futures with{" "}
                <span className="text-gradient">
                  Education
                  <TbArrowBigRightLinesFilled className="inline w-8 h-8 ml-2" />
                </span>
              </h1>

              <p className="hero-subtitle">
                Discover limitless learning opportunities with our comprehensive
                online study platform. Study anywhere, anytime, and achieve your
                academic goals with confidence.
              </p>

              {/* Explore Platform Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="cta-section"
              >
                <Link to="/login">
                  <button className="btn btn-primary btn-large">
                    <TbBrain className="w-5 h-5 mr-2" />
                    Explore Platform
                  </button>
                </Link>
              </motion.div>




            </motion.div>

            {/* Hero Image */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="hero-image"
            >
              <div className="relative">
                <img
                  src={Image1}
                  alt="Students Learning"
                  loading="lazy"
                />

                {/* Floating Elements */}
                <motion.div
                  animate={{ y: [-10, 10, -10] }}
                  transition={{ duration: 4, repeat: Infinity }}
                  className="floating-element"
                  style={{top: '-1rem', left: '-1rem'}}
                >
                  <TbBook style={{color: '#007BFF'}} />
                </motion.div>

                <motion.div
                  animate={{ y: [10, -10, 10] }}
                  transition={{ duration: 3, repeat: Infinity }}
                  className="floating-element"
                  style={{bottom: '-1rem', right: '-1rem'}}
                >
                  <TbTrophy style={{color: '#f59e0b'}} />
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section">
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="stats-grid"
          >
            {[
              { number: "15K+", text: "Active Students" },
              { number: "500+", text: "Expert Teachers" },
              { number: "1000+", text: "Video Lessons" },
              { number: "98%", text: "Success Rate" }
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="stat-item"
              >
                <div className="stat-number">{stat.number}</div>
                <p className="stat-text">{stat.text}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Reviews Section */}
      <section ref={reviewsSectionRef} className="reviews-section">
        <div className="reviews-container">
          <h2 className="reviews-title">
            Reviews from our students
          </h2>
          <div className="reviews-grid">
            {[
              {
                rating: 5,
                text: "BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.",
                user: { name: "Sarah Johnson" }
              },
              {
                rating: 5,
                text: "The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.",
                user: { name: "Michael Chen" }
              },
              {
                rating: 5,
                text: "Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!",
                user: { name: "Amina Hassan" }
              }
            ].map((review, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="review-card"
              >
                <div className="review-rating">
                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>
                    {'★'.repeat(review.rating)}
                  </div>
                </div>
                <div className="review-text">"{review.text}"</div>
                <div className="review-divider"></div>
                <div className="review-author">{review.user?.name}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section ref={contactUsRef} className="contact-section">
        <div className="contact-container">
          <h2 className="contact-title">Contact Us</h2>
          <p className="contact-subtitle">Get in touch with us for any questions or support</p>
          <form className="contact-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label className="form-label">Name</label>
              <input
                type="text"
                name="name"
                placeholder="Your Name"
                className="form-input"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label className="form-label">Email</label>
              <input
                type="email"
                name="email"
                placeholder="Your Email"
                className="form-input"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
            <div className="form-group">
              <label className="form-label">Message</label>
              <textarea
                name="message"
                placeholder="Your Message"
                className="form-input form-textarea"
                value={formData.message}
                onChange={handleChange}
                required
              ></textarea>
            </div>
            <button
              type="submit"
              className="form-submit"
              disabled={loading}
            >
              {loading ? "Sending..." : "Send Message"}
            </button>
            {responseMessage && (
              <p className="response-message" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>
                {responseMessage}
              </p>
            )}
          </form>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <p className="footer-text">
            © 2024 BrainWave Educational Platform. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Home;
