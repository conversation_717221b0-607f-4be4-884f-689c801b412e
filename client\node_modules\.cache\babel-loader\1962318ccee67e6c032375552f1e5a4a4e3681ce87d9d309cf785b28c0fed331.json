{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\StudyMaterials\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { message } from \"antd\";\nimport { motion } from \"framer-motion\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TbDashboard } from \"react-icons/tb\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport AddStudyMaterialForm from \"./AddStudyMaterialForm\";\nimport SubtitleManager from \"./SubtitleManager\";\nimport StudyMaterialManager from \"./StudyMaterialManager\";\nimport EditStudyMaterialForm from \"./EditStudyMaterialForm\";\nimport \"./index.css\";\nimport { FaVideo, FaFileAlt, FaBook, FaPlus, FaGraduationCap, FaClosedCaptioning, FaCog, FaList } from \"react-icons/fa\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AdminStudyMaterials() {\n  _s();\n  var _materialTypes$find;\n  const navigate = useNavigate();\n  const [selectedMaterialType, setSelectedMaterialType] = useState(\"\");\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showSubtitleManager, setShowSubtitleManager] = useState(false);\n  const [showMaterialManager, setShowMaterialManager] = useState(false);\n  const [showEditForm, setShowEditForm] = useState(false);\n  const [selectedMaterial, setSelectedMaterial] = useState(null);\n  const materialTypes = [{\n    key: \"videos\",\n    title: \"Videos\",\n    icon: /*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 13\n    }, this),\n    description: \"Add educational videos for students\",\n    color: \"#e74c3c\"\n  }, {\n    key: \"study-notes\",\n    title: \"Study Notes\",\n    icon: /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this),\n    description: \"Upload study notes and documents\",\n    color: \"#3498db\"\n  }, {\n    key: \"past-papers\",\n    title: \"Past Papers\",\n    icon: /*#__PURE__*/_jsxDEV(FaGraduationCap, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this),\n    description: \"Add past examination papers\",\n    color: \"#9b59b6\"\n  }, {\n    key: \"books\",\n    title: \"Books\",\n    icon: /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this),\n    description: \"Upload textbooks and reference materials\",\n    color: \"#27ae60\"\n  }];\n  const managementOptions = [{\n    key: \"manage-materials\",\n    title: \"Manage Materials\",\n    icon: /*#__PURE__*/_jsxDEV(FaCog, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 13\n    }, this),\n    description: \"Edit, delete, and organize study materials\",\n    color: \"#34495e\"\n  }, {\n    key: \"subtitles\",\n    title: \"Subtitle Management\",\n    icon: /*#__PURE__*/_jsxDEV(FaClosedCaptioning, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 13\n    }, this),\n    description: \"Manage video subtitles and captions\",\n    color: \"#f39c12\"\n  }];\n  const handleMaterialTypeSelect = materialType => {\n    setSelectedMaterialType(materialType);\n    setShowAddForm(true);\n  };\n  const handleManagementOptionSelect = option => {\n    if (option === \"subtitles\") {\n      setShowSubtitleManager(true);\n    } else if (option === \"manage-materials\") {\n      setShowMaterialManager(true);\n    }\n  };\n  const handleFormClose = () => {\n    setShowAddForm(false);\n    setSelectedMaterialType(\"\");\n  };\n  const handleSubtitleManagerClose = () => {\n    setShowSubtitleManager(false);\n  };\n  const handleMaterialManagerClose = () => {\n    setShowMaterialManager(false);\n  };\n  const handleEditMaterial = material => {\n    setSelectedMaterial(material);\n    setShowEditForm(true);\n  };\n  const handleEditFormClose = () => {\n    setShowEditForm(false);\n    setSelectedMaterial(null);\n  };\n  const handleFormSuccess = materialType => {\n    message.success(`${materialType} added successfully!`);\n    setShowAddForm(false);\n    setSelectedMaterialType(\"\");\n  };\n  const handleEditSuccess = materialType => {\n    message.success(`${materialType} updated successfully!`);\n    setShowEditForm(false);\n    setSelectedMaterial(null);\n    // Refresh the material manager if it's open\n    if (showMaterialManager) {\n      // The StudyMaterialManager component will handle its own refresh\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"admin-study-materials\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-4 mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(motion.button, {\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        onClick: () => navigate('/admin/dashboard'),\n        className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\",\n        children: [/*#__PURE__*/_jsxDEV(TbDashboard, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"hidden sm:inline text-sm font-medium\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Study Materials Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), showSubtitleManager ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subtitle-manager-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: handleSubtitleManagerClose,\n          children: \"\\u2190 Back to Material Types\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Subtitle Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(SubtitleManager, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this) : showMaterialManager ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"material-manager-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: handleMaterialManagerClose,\n          children: \"\\u2190 Back to Main Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Study Materials Management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(StudyMaterialManager, {\n        onEdit: handleEditMaterial\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 9\n    }, this) : showEditForm ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"edit-form-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: handleEditFormClose,\n          children: \"\\u2190 Back to Materials List\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(EditStudyMaterialForm, {\n        material: selectedMaterial,\n        onSuccess: handleEditSuccess,\n        onCancel: handleEditFormClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 9\n    }, this) : showAddForm ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"add-form-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"back-button\",\n          onClick: handleFormClose,\n          children: \"\\u2190 Back to Main Menu\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"Add \", (_materialTypes$find = materialTypes.find(t => t.key === selectedMaterialType)) === null || _materialTypes$find === void 0 ? void 0 : _materialTypes$find.title]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(AddStudyMaterialForm, {\n        materialType: selectedMaterialType,\n        onSuccess: handleFormSuccess,\n        onCancel: handleFormClose\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-menu-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Study Materials Administration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Manage your educational content - add new materials or edit existing ones\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"menu-sections\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n              className: \"section-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), \"Add New Materials\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Upload new study materials for students\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"material-types-grid\",\n            children: materialTypes.map(type => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"material-type-card\",\n              onClick: () => handleMaterialTypeSelect(type.key),\n              style: {\n                borderColor: type.color\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-icon\",\n                style: {\n                  color: type.color\n                },\n                children: type.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: type.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: type.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"add-button\",\n                style: {\n                  backgroundColor: type.color\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Add \", type.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 21\n              }, this)]\n            }, type.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"menu-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [/*#__PURE__*/_jsxDEV(FaList, {\n              className: \"section-icon\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), \"Manage Existing Materials\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Edit, delete, and organize your study materials\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"management-options-grid\",\n            children: managementOptions.map(option => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"management-option-card\",\n              onClick: () => handleManagementOptionSelect(option.key),\n              style: {\n                borderColor: option.color\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-icon\",\n                style: {\n                  color: option.color\n                },\n                children: option.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: option.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: option.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"manage-button\",\n                style: {\n                  backgroundColor: option.color\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCog, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Open \", option.title]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 21\n              }, this)]\n            }, option.key, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 5\n  }, this);\n}\n_s(AdminStudyMaterials, \"BN451MKJd88tBCpakgxUaEd1ThM=\", false, function () {\n  return [useNavigate];\n});\n_c = AdminStudyMaterials;\nexport default AdminStudyMaterials;\nvar _c;\n$RefreshReg$(_c, \"AdminStudyMaterials\");", "map": {"version": 3, "names": ["React", "useState", "message", "motion", "useNavigate", "TbDashboard", "Page<PERSON><PERSON>le", "AddStudyMaterialForm", "SubtitleManager", "StudyMaterialManager", "EditStudyMaterialForm", "FaVideo", "FaFileAlt", "FaBook", "FaPlus", "FaGraduationCap", "FaClosedCaptioning", "FaCog", "FaList", "jsxDEV", "_jsxDEV", "AdminStudyMaterials", "_s", "_materialTypes$find", "navigate", "selectedMaterialType", "setSelectedMaterialType", "showAddForm", "setShowAddForm", "showSubtitleManager", "setShowSubtitleManager", "showMaterialManager", "setShowMaterialManager", "showEditForm", "setShowEditForm", "selectedMaterial", "setSelectedMaterial", "materialTypes", "key", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "color", "managementOptions", "handleMaterialTypeSelect", "materialType", "handleManagementOptionSelect", "option", "handleFormClose", "handleSubtitleManagerClose", "handleMaterialManagerClose", "handleEditMaterial", "material", "handleEditFormClose", "handleFormSuccess", "success", "handleEditSuccess", "className", "children", "button", "whileHover", "scale", "whileTap", "onClick", "onEdit", "onSuccess", "onCancel", "find", "t", "map", "type", "style", "borderColor", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/StudyMaterials/index.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { message } from \"antd\";\nimport { motion } from \"framer-motion\";\nimport { useNavigate } from \"react-router-dom\";\nimport { TbDashboard } from \"react-icons/tb\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport AddStudyMaterialForm from \"./AddStudyMaterialForm\";\nimport SubtitleManager from \"./SubtitleManager\";\nimport StudyMaterialManager from \"./StudyMaterialManager\";\nimport EditStudyMaterialForm from \"./EditStudyMaterialForm\";\nimport \"./index.css\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaPlus,\n  FaGraduationCap,\n  FaClosedCaptioning,\n  FaCog,\n  FaList\n} from \"react-icons/fa\";\n\nfunction AdminStudyMaterials() {\n  const navigate = useNavigate();\n  const [selectedMaterialType, setSelectedMaterialType] = useState(\"\");\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [showSubtitleManager, setShowSubtitleManager] = useState(false);\n  const [showMaterialManager, setShowMaterialManager] = useState(false);\n  const [showEditForm, setShowEditForm] = useState(false);\n  const [selectedMaterial, setSelectedMaterial] = useState(null);\n\n  const materialTypes = [\n    {\n      key: \"videos\",\n      title: \"Videos\",\n      icon: <FaVideo />,\n      description: \"Add educational videos for students\",\n      color: \"#e74c3c\"\n    },\n    {\n      key: \"study-notes\",\n      title: \"Study Notes\",\n      icon: <FaFileAlt />,\n      description: \"Upload study notes and documents\",\n      color: \"#3498db\"\n    },\n    {\n      key: \"past-papers\",\n      title: \"Past Papers\",\n      icon: <FaGraduationCap />,\n      description: \"Add past examination papers\",\n      color: \"#9b59b6\"\n    },\n    {\n      key: \"books\",\n      title: \"Books\",\n      icon: <FaBook />,\n      description: \"Upload textbooks and reference materials\",\n      color: \"#27ae60\"\n    }\n  ];\n\n  const managementOptions = [\n    {\n      key: \"manage-materials\",\n      title: \"Manage Materials\",\n      icon: <FaCog />,\n      description: \"Edit, delete, and organize study materials\",\n      color: \"#34495e\"\n    },\n    {\n      key: \"subtitles\",\n      title: \"Subtitle Management\",\n      icon: <FaClosedCaptioning />,\n      description: \"Manage video subtitles and captions\",\n      color: \"#f39c12\"\n    }\n  ];\n\n  const handleMaterialTypeSelect = (materialType) => {\n    setSelectedMaterialType(materialType);\n    setShowAddForm(true);\n  };\n\n  const handleManagementOptionSelect = (option) => {\n    if (option === \"subtitles\") {\n      setShowSubtitleManager(true);\n    } else if (option === \"manage-materials\") {\n      setShowMaterialManager(true);\n    }\n  };\n\n  const handleFormClose = () => {\n    setShowAddForm(false);\n    setSelectedMaterialType(\"\");\n  };\n\n  const handleSubtitleManagerClose = () => {\n    setShowSubtitleManager(false);\n  };\n\n  const handleMaterialManagerClose = () => {\n    setShowMaterialManager(false);\n  };\n\n  const handleEditMaterial = (material) => {\n    setSelectedMaterial(material);\n    setShowEditForm(true);\n  };\n\n  const handleEditFormClose = () => {\n    setShowEditForm(false);\n    setSelectedMaterial(null);\n  };\n\n  const handleFormSuccess = (materialType) => {\n    message.success(`${materialType} added successfully!`);\n    setShowAddForm(false);\n    setSelectedMaterialType(\"\");\n  };\n\n  const handleEditSuccess = (materialType) => {\n    message.success(`${materialType} updated successfully!`);\n    setShowEditForm(false);\n    setSelectedMaterial(null);\n    // Refresh the material manager if it's open\n    if (showMaterialManager) {\n      // The StudyMaterialManager component will handle its own refresh\n    }\n  };\n\n  return (\n    <div className=\"admin-study-materials\">\n      <div className=\"flex items-center gap-4 mb-4\">\n        {/* Dashboard Shortcut */}\n        <motion.button\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          onClick={() => navigate('/admin/dashboard')}\n          className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\n        >\n          <TbDashboard className=\"w-4 h-4\" />\n          <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\n        </motion.button>\n\n        <PageTitle title=\"Study Materials Management\" />\n      </div>\n      \n      {showSubtitleManager ? (\n        <div className=\"subtitle-manager-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleSubtitleManagerClose}\n            >\n              ← Back to Material Types\n            </button>\n            <h2>Subtitle Management</h2>\n          </div>\n          <SubtitleManager />\n        </div>\n      ) : showMaterialManager ? (\n        <div className=\"material-manager-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleMaterialManagerClose}\n            >\n              ← Back to Main Menu\n            </button>\n            <h2>Study Materials Management</h2>\n          </div>\n          <StudyMaterialManager onEdit={handleEditMaterial} />\n        </div>\n      ) : showEditForm ? (\n        <div className=\"edit-form-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleEditFormClose}\n            >\n              ← Back to Materials List\n            </button>\n          </div>\n          <EditStudyMaterialForm\n            material={selectedMaterial}\n            onSuccess={handleEditSuccess}\n            onCancel={handleEditFormClose}\n          />\n        </div>\n      ) : showAddForm ? (\n        <div className=\"add-form-container\">\n          <div className=\"form-header\">\n            <button\n              className=\"back-button\"\n              onClick={handleFormClose}\n            >\n              ← Back to Main Menu\n            </button>\n            <h2>\n              Add {materialTypes.find(t => t.key === selectedMaterialType)?.title}\n            </h2>\n          </div>\n\n          <AddStudyMaterialForm\n            materialType={selectedMaterialType}\n            onSuccess={handleFormSuccess}\n            onCancel={handleFormClose}\n          />\n        </div>\n      ) : (\n        <div className=\"main-menu-container\">\n          <div className=\"header-section\">\n            <h2>Study Materials Administration</h2>\n            <p>Manage your educational content - add new materials or edit existing ones</p>\n          </div>\n\n          <div className=\"menu-sections\">\n            <div className=\"menu-section\">\n              <h3>\n                <FaPlus className=\"section-icon\" />\n                Add New Materials\n              </h3>\n              <p>Upload new study materials for students</p>\n              <div className=\"material-types-grid\">\n                {materialTypes.map((type) => (\n                  <div\n                    key={type.key}\n                    className=\"material-type-card\"\n                    onClick={() => handleMaterialTypeSelect(type.key)}\n                    style={{ borderColor: type.color }}\n                  >\n                    <div className=\"card-icon\" style={{ color: type.color }}>\n                      {type.icon}\n                    </div>\n                    <h4>{type.title}</h4>\n                    <p>{type.description}</p>\n                    <div className=\"add-button\" style={{ backgroundColor: type.color }}>\n                      <FaPlus />\n                      <span>Add {type.title}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"menu-section\">\n              <h3>\n                <FaList className=\"section-icon\" />\n                Manage Existing Materials\n              </h3>\n              <p>Edit, delete, and organize your study materials</p>\n              <div className=\"management-options-grid\">\n                {managementOptions.map((option) => (\n                  <div\n                    key={option.key}\n                    className=\"management-option-card\"\n                    onClick={() => handleManagementOptionSelect(option.key)}\n                    style={{ borderColor: option.color }}\n                  >\n                    <div className=\"card-icon\" style={{ color: option.color }}>\n                      {option.icon}\n                    </div>\n                    <h4>{option.title}</h4>\n                    <p>{option.description}</p>\n                    <div className=\"manage-button\" style={{ backgroundColor: option.color }}>\n                      <FaCog />\n                      <span>Open {option.title}</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default AdminStudyMaterials;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAO,aAAa;AACpB,SACEC,OAAO,EACPC,SAAS,EACTC,MAAM,EACNC,MAAM,EACNC,eAAe,EACfC,kBAAkB,EAClBC,KAAK,EACLC,MAAM,QACD,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExB,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EAC7B,MAAMC,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACqB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC4B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAE9D,MAAMoC,aAAa,GAAG,CACpB;IACEC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,QAAQ;IACfC,IAAI,eAAEpB,OAAA,CAACT,OAAO;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,WAAW,EAAE,qCAAqC;IAClDC,KAAK,EAAE;EACT,CAAC,EACD;IACER,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEpB,OAAA,CAACR,SAAS;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,WAAW,EAAE,kCAAkC;IAC/CC,KAAK,EAAE;EACT,CAAC,EACD;IACER,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEpB,OAAA,CAACL,eAAe;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,WAAW,EAAE,6BAA6B;IAC1CC,KAAK,EAAE;EACT,CAAC,EACD;IACER,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,OAAO;IACdC,IAAI,eAAEpB,OAAA,CAACP,MAAM;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBC,WAAW,EAAE,0CAA0C;IACvDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,iBAAiB,GAAG,CACxB;IACET,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,eAAEpB,OAAA,CAACH,KAAK;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACfC,WAAW,EAAE,4CAA4C;IACzDC,KAAK,EAAE;EACT,CAAC,EACD;IACER,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,qBAAqB;IAC5BC,IAAI,eAAEpB,OAAA,CAACJ,kBAAkB;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,WAAW,EAAE,qCAAqC;IAClDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAME,wBAAwB,GAAIC,YAAY,IAAK;IACjDvB,uBAAuB,CAACuB,YAAY,CAAC;IACrCrB,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMsB,4BAA4B,GAAIC,MAAM,IAAK;IAC/C,IAAIA,MAAM,KAAK,WAAW,EAAE;MAC1BrB,sBAAsB,CAAC,IAAI,CAAC;IAC9B,CAAC,MAAM,IAAIqB,MAAM,KAAK,kBAAkB,EAAE;MACxCnB,sBAAsB,CAAC,IAAI,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoB,eAAe,GAAGA,CAAA,KAAM;IAC5BxB,cAAc,CAAC,KAAK,CAAC;IACrBF,uBAAuB,CAAC,EAAE,CAAC;EAC7B,CAAC;EAED,MAAM2B,0BAA0B,GAAGA,CAAA,KAAM;IACvCvB,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMwB,0BAA0B,GAAGA,CAAA,KAAM;IACvCtB,sBAAsB,CAAC,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMuB,kBAAkB,GAAIC,QAAQ,IAAK;IACvCpB,mBAAmB,CAACoB,QAAQ,CAAC;IAC7BtB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMuB,mBAAmB,GAAGA,CAAA,KAAM;IAChCvB,eAAe,CAAC,KAAK,CAAC;IACtBE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMsB,iBAAiB,GAAIT,YAAY,IAAK;IAC1C/C,OAAO,CAACyD,OAAO,CAAE,GAAEV,YAAa,sBAAqB,CAAC;IACtDrB,cAAc,CAAC,KAAK,CAAC;IACrBF,uBAAuB,CAAC,EAAE,CAAC;EAC7B,CAAC;EAED,MAAMkC,iBAAiB,GAAIX,YAAY,IAAK;IAC1C/C,OAAO,CAACyD,OAAO,CAAE,GAAEV,YAAa,wBAAuB,CAAC;IACxDf,eAAe,CAAC,KAAK,CAAC;IACtBE,mBAAmB,CAAC,IAAI,CAAC;IACzB;IACA,IAAIL,mBAAmB,EAAE;MACvB;IAAA;EAEJ,CAAC;EAED,oBACEX,OAAA;IAAKyC,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC1C,OAAA;MAAKyC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAE3C1C,OAAA,CAACjB,MAAM,CAAC4D,MAAM;QACZC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAC1BE,OAAO,EAAEA,CAAA,KAAM3C,QAAQ,CAAC,kBAAkB,CAAE;QAC5CqC,SAAS,EAAC,gIAAgI;QAAAC,QAAA,gBAE1I1C,OAAA,CAACf,WAAW;UAACwD,SAAS,EAAC;QAAS;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnCxB,OAAA;UAAMyC,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAS;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eAEhBxB,OAAA,CAACd,SAAS;QAACiC,KAAK,EAAC;MAA4B;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC,EAELf,mBAAmB,gBAClBT,OAAA;MAAKyC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1C,OAAA;UACEyC,SAAS,EAAC,aAAa;UACvBM,OAAO,EAAEd,0BAA2B;UAAAS,QAAA,EACrC;QAED;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA;UAAA0C,QAAA,EAAI;QAAmB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACNxB,OAAA,CAACZ,eAAe;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,GACJb,mBAAmB,gBACrBX,OAAA;MAAKyC,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzC1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1C,OAAA;UACEyC,SAAS,EAAC,aAAa;UACvBM,OAAO,EAAEb,0BAA2B;UAAAQ,QAAA,EACrC;QAED;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA;UAAA0C,QAAA,EAAI;QAA0B;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACNxB,OAAA,CAACX,oBAAoB;QAAC2D,MAAM,EAAEb;MAAmB;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,GACJX,YAAY,gBACdb,OAAA;MAAKyC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1B1C,OAAA;UACEyC,SAAS,EAAC,aAAa;UACvBM,OAAO,EAAEV,mBAAoB;UAAAK,QAAA,EAC9B;QAED;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNxB,OAAA,CAACV,qBAAqB;QACpB8C,QAAQ,EAAErB,gBAAiB;QAC3BkC,SAAS,EAAET,iBAAkB;QAC7BU,QAAQ,EAAEb;MAAoB;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,GACJjB,WAAW,gBACbP,OAAA;MAAKyC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC1C,OAAA;QAAKyC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1C,OAAA;UACEyC,SAAS,EAAC,aAAa;UACvBM,OAAO,EAAEf,eAAgB;UAAAU,QAAA,EAC1B;QAED;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxB,OAAA;UAAA0C,QAAA,GAAI,MACE,GAAAvC,mBAAA,GAACc,aAAa,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClC,GAAG,KAAKb,oBAAoB,CAAC,cAAAF,mBAAA,uBAAvDA,mBAAA,CAAyDgB,KAAK;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENxB,OAAA,CAACb,oBAAoB;QACnB0C,YAAY,EAAExB,oBAAqB;QACnC4C,SAAS,EAAEX,iBAAkB;QAC7BY,QAAQ,EAAElB;MAAgB;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,gBAENxB,OAAA;MAAKyC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC1C,OAAA;QAAKyC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B1C,OAAA;UAAA0C,QAAA,EAAI;QAA8B;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCxB,OAAA;UAAA0C,QAAA,EAAG;QAAyE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eAENxB,OAAA;QAAKyC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B1C,OAAA;UAAKyC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA,CAACN,MAAM;cAAC+C,SAAS,EAAC;YAAc;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxB,OAAA;YAAA0C,QAAA,EAAG;UAAuC;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9CxB,OAAA;YAAKyC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EACjCzB,aAAa,CAACoC,GAAG,CAAEC,IAAI,iBACtBtD,OAAA;cAEEyC,SAAS,EAAC,oBAAoB;cAC9BM,OAAO,EAAEA,CAAA,KAAMnB,wBAAwB,CAAC0B,IAAI,CAACpC,GAAG,CAAE;cAClDqC,KAAK,EAAE;gBAAEC,WAAW,EAAEF,IAAI,CAAC5B;cAAM,CAAE;cAAAgB,QAAA,gBAEnC1C,OAAA;gBAAKyC,SAAS,EAAC,WAAW;gBAACc,KAAK,EAAE;kBAAE7B,KAAK,EAAE4B,IAAI,CAAC5B;gBAAM,CAAE;gBAAAgB,QAAA,EACrDY,IAAI,CAAClC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACNxB,OAAA;gBAAA0C,QAAA,EAAKY,IAAI,CAACnC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrBxB,OAAA;gBAAA0C,QAAA,EAAIY,IAAI,CAAC7B;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBxB,OAAA;gBAAKyC,SAAS,EAAC,YAAY;gBAACc,KAAK,EAAE;kBAAEE,eAAe,EAAEH,IAAI,CAAC5B;gBAAM,CAAE;gBAAAgB,QAAA,gBACjE1C,OAAA,CAACN,MAAM;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACVxB,OAAA;kBAAA0C,QAAA,GAAM,MAAI,EAACY,IAAI,CAACnC,KAAK;gBAAA;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA,GAbD8B,IAAI,CAACpC,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxB,OAAA;UAAKyC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA,CAACF,MAAM;cAAC2C,SAAS,EAAC;YAAc;cAAApB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6BAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLxB,OAAA;YAAA0C,QAAA,EAAG;UAA+C;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACtDxB,OAAA;YAAKyC,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EACrCf,iBAAiB,CAAC0B,GAAG,CAAEtB,MAAM,iBAC5B/B,OAAA;cAEEyC,SAAS,EAAC,wBAAwB;cAClCM,OAAO,EAAEA,CAAA,KAAMjB,4BAA4B,CAACC,MAAM,CAACb,GAAG,CAAE;cACxDqC,KAAK,EAAE;gBAAEC,WAAW,EAAEzB,MAAM,CAACL;cAAM,CAAE;cAAAgB,QAAA,gBAErC1C,OAAA;gBAAKyC,SAAS,EAAC,WAAW;gBAACc,KAAK,EAAE;kBAAE7B,KAAK,EAAEK,MAAM,CAACL;gBAAM,CAAE;gBAAAgB,QAAA,EACvDX,MAAM,CAACX;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNxB,OAAA;gBAAA0C,QAAA,EAAKX,MAAM,CAACZ;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvBxB,OAAA;gBAAA0C,QAAA,EAAIX,MAAM,CAACN;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BxB,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAACc,KAAK,EAAE;kBAAEE,eAAe,EAAE1B,MAAM,CAACL;gBAAM,CAAE;gBAAAgB,QAAA,gBACtE1C,OAAA,CAACH,KAAK;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACTxB,OAAA;kBAAA0C,QAAA,GAAM,OAAK,EAACX,MAAM,CAACZ,KAAK;gBAAA;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA,GAbDO,MAAM,CAACb,GAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcZ,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACtB,EAAA,CAhQQD,mBAAmB;EAAA,QACTjB,WAAW;AAAA;AAAA0E,EAAA,GADrBzD,mBAAmB;AAkQ5B,eAAeA,mBAAmB;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}