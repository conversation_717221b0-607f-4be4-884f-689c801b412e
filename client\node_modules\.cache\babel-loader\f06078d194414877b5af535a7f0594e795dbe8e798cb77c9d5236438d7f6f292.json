{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\Exams\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message, Table } from \"antd\";\nimport React, { useEffect } from \"react\";\nimport { useDispatch } from \"react-redux\";\nimport { useNavigate } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbDashboard, TbPlus } from \"react-icons/tb\";\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Exams() {\n  _s();\n  const navigate = useNavigate();\n  const [exams, setExams] = React.useState([]);\n  const dispatch = useDispatch();\n  const getExamsData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllExams();\n      dispatch(HideLoading());\n      if (response.success) {\n        setExams(response.data.reverse());\n        console.log(response, \"exam\");\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const deleteExam = async examId => {\n    try {\n      dispatch(ShowLoading());\n      const response = await deleteExamById({\n        examId\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        message.success(response.message);\n        getExamsData();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const columns = [{\n    title: \"Exam Name\",\n    dataIndex: \"name\"\n  }, {\n    title: \"Duration\",\n    dataIndex: \"duration\",\n    render: duration => `${Math.round(duration / 60)} min`\n  }, {\n    title: \"Class\",\n    dataIndex: \"class\"\n  }, {\n    title: \"Category\",\n    dataIndex: \"category\"\n  }, {\n    title: \"Total Marks\",\n    dataIndex: \"totalMarks\"\n  }, {\n    title: \"Passing Marks\",\n    dataIndex: \"passingMarks\"\n  }, {\n    title: \"Action\",\n    dataIndex: \"action\",\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"ri-pencil-line\",\n        onClick: () => navigate(`/admin/exams/edit/${record._id}`)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"ri-delete-bin-line\",\n        onClick: () => deleteExam(record._id)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 9\n    }, this)\n  }];\n  useEffect(() => {\n    getExamsData();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between mt-2 items-end\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => navigate('/admin/dashboard'),\n          className: \"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\",\n          children: [/*#__PURE__*/_jsxDEV(TbDashboard, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"hidden sm:inline text-sm font-medium\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PageTitle, {\n          title: \"Exams\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n        whileHover: {\n          scale: 1.05\n        },\n        whileTap: {\n          scale: 0.95\n        },\n        className: \"primary-outlined-btn flex items-center gap-2\",\n        onClick: () => navigate(\"/admin/exams/add\"),\n        children: [/*#__PURE__*/_jsxDEV(TbPlus, {\n          className: \"w-4 h-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), \"Add Exam\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"divider\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: exams\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this);\n}\n_s(Exams, \"zUsyM+RYIXueOJI+SO0V1xlQ01A=\", false, function () {\n  return [useNavigate, useDispatch];\n});\n_c = Exams;\nexport default Exams;\nvar _c;\n$RefreshReg$(_c, \"Exams\");", "map": {"version": 3, "names": ["message", "Table", "React", "useEffect", "useDispatch", "useNavigate", "motion", "TbDashboard", "TbPlus", "deleteExamById", "getAllExams", "Page<PERSON><PERSON>le", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "navigate", "exams", "setExams", "useState", "dispatch", "getExamsData", "response", "success", "data", "reverse", "console", "log", "error", "deleteExam", "examId", "columns", "title", "dataIndex", "render", "duration", "Math", "round", "text", "record", "className", "children", "onClick", "_id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "dataSource", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/Exams/index.js"], "sourcesContent": ["import { message, Table } from \"antd\";\r\nimport React, { useEffect } from \"react\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport { TbDashboard, TbPlus } from \"react-icons/tb\";\r\nimport { deleteExamById, getAllExams } from \"../../../apicalls/exams\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\n\r\nfunction Exams() {\r\n  const navigate = useNavigate();\r\n  const [exams, setExams] = React.useState([]);\r\n  const dispatch = useDispatch();\r\n\r\n  const getExamsData = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await getAllExams();\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        setExams(response.data.reverse());\r\n        console.log(response, \"exam\");\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const deleteExam = async (examId) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await deleteExamById({\r\n        examId,\r\n      });\r\n      dispatch(HideLoading());\r\n      if (response.success) {\r\n        message.success(response.message);\r\n        getExamsData();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n  const columns = [\r\n    {\r\n      title: \"Exam Name\",\r\n      dataIndex: \"name\",\r\n    },\r\n    {\r\n      title: \"Duration\",\r\n      dataIndex: \"duration\",\r\n      render: (duration) => `${Math.round(duration / 60)} min`,\r\n    },\r\n    {\r\n      title: \"Class\",\r\n      dataIndex: \"class\",\r\n    },\r\n    {\r\n      title: \"Category\",\r\n      dataIndex: \"category\",\r\n    },\r\n    {\r\n      title: \"Total Marks\",\r\n      dataIndex: \"totalMarks\",\r\n    },\r\n    {\r\n      title: \"Passing Marks\",\r\n      dataIndex: \"passingMarks\",\r\n    },\r\n    {\r\n      title: \"Action\",\r\n      dataIndex: \"action\",\r\n      render: (text, record) => (\r\n        <div className=\"flex gap-2\">\r\n          <i\r\n            className=\"ri-pencil-line\"\r\n            onClick={() => navigate(`/admin/exams/edit/${record._id}`)}\r\n          ></i>\r\n          <i\r\n            className=\"ri-delete-bin-line\"\r\n            onClick={() => deleteExam(record._id)}\r\n          ></i>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n  useEffect(() => {\r\n    getExamsData();\r\n  }, []);\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between mt-2 items-end\">\r\n        <div className=\"flex items-center gap-4\">\r\n          {/* Dashboard Shortcut */}\r\n          <motion.button\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            onClick={() => navigate('/admin/dashboard')}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 shadow-md\"\r\n          >\r\n            <TbDashboard className=\"w-4 h-4\" />\r\n            <span className=\"hidden sm:inline text-sm font-medium\">Dashboard</span>\r\n          </motion.button>\r\n\r\n          <PageTitle title=\"Exams\" />\r\n        </div>\r\n\r\n        <motion.button\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"primary-outlined-btn flex items-center gap-2\"\r\n          onClick={() => navigate(\"/admin/exams/add\")}\r\n        >\r\n          <TbPlus className=\"w-4 h-4\" />\r\n          Add Exam\r\n        </motion.button>\r\n      </div>\r\n      <div className=\"divider\"></div>\r\n\r\n      <Table columns={columns} dataSource={exams} />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Exams;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,WAAW,EAAEC,MAAM,QAAQ,gBAAgB;AACpD,SAASC,cAAc,EAAEC,WAAW,QAAQ,yBAAyB;AACrE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAE9B,MAAMmB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFD,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMW,QAAQ,GAAG,MAAMd,WAAW,CAAC,CAAC;MACpCY,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIY,QAAQ,CAACC,OAAO,EAAE;QACpBL,QAAQ,CAACI,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;QACjCC,OAAO,CAACC,GAAG,CAACL,QAAQ,EAAE,MAAM,CAAC;MAC/B,CAAC,MAAM;QACLxB,OAAO,CAAC8B,KAAK,CAACN,QAAQ,CAACxB,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdR,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC;MACvBZ,OAAO,CAAC8B,KAAK,CAACA,KAAK,CAAC9B,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM+B,UAAU,GAAG,MAAOC,MAAM,IAAK;IACnC,IAAI;MACFV,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMW,QAAQ,GAAG,MAAMf,cAAc,CAAC;QACpCuB;MACF,CAAC,CAAC;MACFV,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIY,QAAQ,CAACC,OAAO,EAAE;QACpBzB,OAAO,CAACyB,OAAO,CAACD,QAAQ,CAACxB,OAAO,CAAC;QACjCuB,YAAY,CAAC,CAAC;MAChB,CAAC,MAAM;QACLvB,OAAO,CAAC8B,KAAK,CAACN,QAAQ,CAACxB,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdR,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC;MACvBZ,OAAO,CAAC8B,KAAK,CAACA,KAAK,CAAC9B,OAAO,CAAC;IAC9B;EACF,CAAC;EACD,MAAMiC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,WAAW;IAClBC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,SAAS,EAAE,UAAU;IACrBC,MAAM,EAAGC,QAAQ,IAAM,GAAEC,IAAI,CAACC,KAAK,CAACF,QAAQ,GAAG,EAAE,CAAE;EACrD,CAAC,EACD;IACEH,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,UAAU;IACjBC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,aAAa;IACpBC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,eAAe;IACtBC,SAAS,EAAE;EACb,CAAC,EACD;IACED,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,QAAQ;IACnBC,MAAM,EAAEA,CAACI,IAAI,EAAEC,MAAM,kBACnB1B,OAAA;MAAK2B,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB5B,OAAA;QACE2B,SAAS,EAAC,gBAAgB;QAC1BE,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAE,qBAAoBuB,MAAM,CAACI,GAAI,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eACLlC,OAAA;QACE2B,SAAS,EAAC,oBAAoB;QAC9BE,OAAO,EAAEA,CAAA,KAAMb,UAAU,CAACU,MAAM,CAACI,GAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAET,CAAC,CACF;EACD9C,SAAS,CAAC,MAAM;IACdoB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACN,oBACER,OAAA;IAAA4B,QAAA,gBACE5B,OAAA;MAAK2B,SAAS,EAAC,qCAAqC;MAAAC,QAAA,gBAClD5B,OAAA;QAAK2B,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBAEtC5B,OAAA,CAACT,MAAM,CAAC4C,MAAM;UACZC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BR,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,kBAAkB,CAAE;UAC5CwB,SAAS,EAAC,gIAAgI;UAAAC,QAAA,gBAE1I5B,OAAA,CAACR,WAAW;YAACmC,SAAS,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnClC,OAAA;YAAM2B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAS;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEhBlC,OAAA,CAACJ,SAAS;UAACuB,KAAK,EAAC;QAAO;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAENlC,OAAA,CAACT,MAAM,CAAC4C,MAAM;QACZC,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAK,CAAE;QAC5BC,QAAQ,EAAE;UAAED,KAAK,EAAE;QAAK,CAAE;QAC1BV,SAAS,EAAC,8CAA8C;QACxDE,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,kBAAkB,CAAE;QAAAyB,QAAA,gBAE5C5B,OAAA,CAACP,MAAM;UAACkC,SAAS,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,YAEhC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eACNlC,OAAA;MAAK2B,SAAS,EAAC;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE/BlC,OAAA,CAACd,KAAK;MAACgC,OAAO,EAAEA,OAAQ;MAACqB,UAAU,EAAEnC;IAAM;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3C,CAAC;AAEV;AAAChC,EAAA,CAvHQD,KAAK;EAAA,QACKX,WAAW,EAEXD,WAAW;AAAA;AAAAmD,EAAA,GAHrBvC,KAAK;AAyHd,eAAeA,KAAK;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}