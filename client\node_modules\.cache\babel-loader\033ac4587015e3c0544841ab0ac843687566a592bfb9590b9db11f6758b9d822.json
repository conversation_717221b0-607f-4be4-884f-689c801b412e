{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Home\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport \"./index.css\";\nimport { Link } from \"react-router-dom\";\nimport { motion } from \"framer-motion\";\nimport { TbArrowBigRightLinesFilled, TbBrain, TbBook, TbTrophy, TbUsers, TbStar, TbSchool } from \"react-icons/tb\";\nimport { message } from \"antd\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllReviews } from \"../../../apicalls/reviews\";\nimport Image1 from \"../../../assets/collage-1.png\";\nimport { contactUs } from \"../../../apicalls/users\";\nimport NotificationBell from \"../../../components/common/NotificationBell\";\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\nimport { OptimizedImage } from \"../../../components/modern/PerformanceMonitor\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const homeSectionRef = useRef(null);\n  const reviewsSectionRef = useRef(null);\n  const contactUsRef = useRef(null);\n  const dispatch = useDispatch();\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    message: \"\"\n  });\n  const [loading, setLoading] = useState(false);\n  const [responseMessage, setResponseMessage] = useState(\"\");\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Check if user is logged in\n  const isLoggedIn = !!user;\n\n  // Optimize reviews loading - only fetch when reviews section is visible\n  useEffect(() => {\n    // Delay reviews loading to improve initial page load\n    const timer = setTimeout(() => {\n      getReviews();\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n  const getReviews = async () => {\n    dispatch(ShowLoading());\n    try {\n      const response = await getAllReviews();\n      if (response.success) {\n        setReviews(response.data);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  const scrollToSection = (ref, offset = 80) => {\n    if (ref !== null && ref !== void 0 && ref.current) {\n      const sectionTop = ref.current.offsetTop;\n      window.scrollTo({\n        top: sectionTop - offset,\n        behavior: \"smooth\"\n      });\n      // Close mobile menu after clicking\n      setMenuOpen(false);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setResponseMessage(\"\");\n    try {\n      const data = await contactUs(formData);\n      if (data.success) {\n        message.success(\"Message sent successfully!\");\n        setResponseMessage(\"Message sent successfully!\");\n        setFormData({\n          name: \"\",\n          email: \"\",\n          message: \"\"\n        });\n      } else {\n        setResponseMessage(data.message || \"Something went wrong.\");\n      }\n    } catch (error) {\n      setResponseMessage(\"Error sending message. Please try again.\");\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"Home\",\n    children: [/*#__PURE__*/_jsxDEV(motion.nav, {\n      initial: {\n        y: -20,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      className: \"nav-header fixed w-full top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between h-16\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(homeSectionRef),\n              className: \"nav-item\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(reviewsSectionRef),\n              className: \"nav-item\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"nav-item\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/\",\n              className: \"flex items-center space-x-2 sm:space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-black/20\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute top-0 left-0 w-full h-1/3 bg-green-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative brainwave-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\",\n                  style: {\n                    fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\n                    letterSpacing: '-0.02em'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block\",\n                    initial: {\n                      opacity: 0,\n                      x: -30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(59, 130, 246, 0.5)\", \"0 0 20px rgba(59, 130, 246, 0.8)\", \"0 0 10px rgba(59, 130, 246, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.3,\n                      textShadow: {\n                        duration: 2,\n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, -2, 2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#1f2937',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\n                    },\n                    children: [\"Brain\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -right-1 w-2 h-2 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1.2, 0.5],\n                        backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\n                      },\n                      transition: {\n                        duration: 1.5,\n                        repeat: Infinity,\n                        delay: 2\n                      },\n                      style: {\n                        backgroundColor: '#3b82f6',\n                        boxShadow: '0 0 10px #3b82f6'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 168,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.span, {\n                    className: \"relative inline-block ml-1\",\n                    initial: {\n                      opacity: 0,\n                      x: 30,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      x: 0,\n                      scale: 1,\n                      textShadow: [\"0 0 10px rgba(16, 185, 129, 0.5)\", \"0 0 20px rgba(16, 185, 129, 0.8)\", \"0 0 10px rgba(16, 185, 129, 0.5)\"]\n                    },\n                    transition: {\n                      duration: 1,\n                      delay: 0.6,\n                      textShadow: {\n                        duration: 2.5,\n                        repeat: Infinity,\n                        ease: \"easeInOut\",\n                        delay: 0.5\n                      }\n                    },\n                    whileHover: {\n                      scale: 1.1,\n                      rotate: [0, 2, -2, 0],\n                      transition: {\n                        duration: 0.3\n                      }\n                    },\n                    style: {\n                      color: '#10b981',\n                      fontWeight: '900',\n                      textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\n                    },\n                    children: [\"wave\", /*#__PURE__*/_jsxDEV(motion.div, {\n                      className: \"absolute -top-1 -left-1 w-1 h-1 rounded-full\",\n                      animate: {\n                        opacity: [0, 1, 0],\n                        scale: [0.5, 1, 0.5],\n                        x: [0, 10, 0],\n                        backgroundColor: ['#10b981', '#34d399', '#10b981']\n                      },\n                      transition: {\n                        duration: 2,\n                        repeat: Infinity,\n                        delay: 3\n                      },\n                      style: {\n                        backgroundColor: '#10b981',\n                        boxShadow: '0 0 8px #10b981'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"absolute -bottom-1 left-0 h-1 rounded-full\",\n                  initial: {\n                    width: 0,\n                    opacity: 0\n                  },\n                  animate: {\n                    width: '100%',\n                    opacity: 1,\n                    boxShadow: ['0 0 10px rgba(16, 185, 129, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(16, 185, 129, 0.5)']\n                  },\n                  transition: {\n                    duration: 1.5,\n                    delay: 1.2,\n                    boxShadow: {\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"easeInOut\"\n                    }\n                  },\n                  style: {\n                    background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\n                    boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"/favicon.png\",\n                  alt: \"Brainwave Logo\",\n                  className: \"w-full h-full object-cover\",\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm sm:text-base md:text-lg\",\n                  style: {\n                    display: 'none'\n                  },\n                  children: \"\\uD83E\\uDDE0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 sm:space-x-4\",\n            children: [isLoggedIn && !(user !== null && user !== void 0 && user.isAdmin) && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.2\n              },\n              children: /*#__PURE__*/_jsxDEV(NotificationBell, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 17\n            }, this), isLoggedIn && /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.8\n              },\n              animate: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: 0.3\n              },\n              className: \"flex items-center space-x-2 sm:space-x-3 group cursor-pointer\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                  user: user,\n                  size: \"sm\",\n                  showOnlineStatus: true,\n                  className: \"ring-2 ring-blue-200 hover:ring-blue-300 transition-all duration-300\",\n                  style: {\n                    width: '36px',\n                    height: '36px',\n                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hidden sm:block text-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 truncate max-w-[120px] lg:max-w-[160px]\",\n                  children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\",\n                  children: user !== null && user !== void 0 && user.isAdmin ? 'Administrator' : `Class ${(user === null || user === void 0 ? void 0 : user.class) || 'N/A'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), menuOpen && /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: -20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          className: \"md:hidden mobile-nav\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(homeSectionRef),\n              className: \"nav-item\",\n              children: \"Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(reviewsSectionRef),\n              className: \"nav-item\",\n              children: \"Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => scrollToSection(contactUsRef),\n              className: \"nav-item\",\n              children: \"Contact Us\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), !isLoggedIn && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col space-y-2 mt-4 pt-4 border-t border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"nav-item text-center\",\n                onClick: () => setMenuOpen(false),\n                children: \"Login\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/register\",\n                className: \"nav-item text-center bg-blue-600 text-white rounded-lg\",\n                onClick: () => setMenuOpen(false),\n                children: \"Register\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: homeSectionRef,\n      className: \"hero-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-grid\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            className: \"hero-content\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.2\n              },\n              className: \"hero-badge\",\n              children: [/*#__PURE__*/_jsxDEV(TbSchool, {\n                className: \"w-5 h-5 mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), \"#1 Educational Platform in Tanzania\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"hero-title\",\n              children: [\"Fueling Bright Futures with\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gradient\",\n                children: [\"Education\", /*#__PURE__*/_jsxDEV(TbArrowBigRightLinesFilled, {\n                  className: \"inline w-8 h-8 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"hero-subtitle\",\n              children: \"Discover limitless learning opportunities with our comprehensive online study platform. Study anywhere, anytime, and achieve your academic goals with confidence.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.4\n              },\n              className: \"cta-section\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-primary btn-large\",\n                  children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                    className: \"w-5 h-5 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 21\n                  }, this), \"Explore Platform\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: 0.6\n              },\n              className: \"trust-indicators\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"15K+ Students\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"4.9/5 Rating\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"trust-indicator\",\n                children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Award Winning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.3\n            },\n            className: \"hero-image\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(LazyWrapper, {\n                children: /*#__PURE__*/_jsxDEV(OptimizedImage, {\n                  src: Image1,\n                  alt: \"Students Learning\",\n                  className: \"w-full h-auto\",\n                  loading: \"lazy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [-10, 10, -10]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  top: '-1rem',\n                  left: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbBook, {\n                  style: {\n                    color: '#007BFF'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  y: [10, -10, 10]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity\n                },\n                className: \"floating-element\",\n                style: {\n                  bottom: '-1rem',\n                  right: '-1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                  style: {\n                    color: '#f59e0b'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"stats-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 50\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          viewport: {\n            once: true\n          },\n          className: \"stats-grid\",\n          children: [{\n            number: \"15K+\",\n            text: \"Active Students\"\n          }, {\n            number: \"500+\",\n            text: \"Expert Teachers\"\n          }, {\n            number: \"1000+\",\n            text: \"Video Lessons\"\n          }, {\n            number: \"98%\",\n            text: \"Success Rate\"\n          }].map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: index * 0.1\n            },\n            viewport: {\n              once: true\n            },\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"stat-text\",\n              children: stat.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: reviewsSectionRef,\n      className: \"reviews-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"reviews-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"reviews-title\",\n          children: \"Reviews from our students\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reviews-grid\",\n          children: [{\n            rating: 5,\n            text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\n            user: {\n              name: \"Sarah Johnson\"\n            }\n          }, {\n            rating: 5,\n            text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\n            user: {\n              name: \"Michael Chen\"\n            }\n          }, {\n            rating: 5,\n            text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\n            user: {\n              name: \"Amina Hassan\"\n            }\n          }].map((review, index) => {\n            var _review$user;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 30\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              viewport: {\n                once: true\n              },\n              className: \"review-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-rating\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#f59e0b',\n                    fontSize: '1.25rem'\n                  },\n                  children: '★'.repeat(review.rating)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-text\",\n                children: [\"\\\"\", review.text, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-divider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"review-author\",\n                children: (_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 565,\n                columnNumber: 17\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      ref: contactUsRef,\n      className: \"contact-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"contact-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"contact-title\",\n          children: \"Contact Us\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"contact-subtitle\",\n          children: \"Get in touch with us for any questions or support\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          className: \"contact-form\",\n          onSubmit: handleSubmit,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"name\",\n              placeholder: \"Your Name\",\n              className: \"form-input\",\n              value: formData.name,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: \"email\",\n              placeholder: \"Your Email\",\n              className: \"form-input\",\n              value: formData.email,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Message\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: \"message\",\n              placeholder: \"Your Message\",\n              className: \"form-input form-textarea\",\n              value: formData.message,\n              onChange: handleChange,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"form-submit\",\n            disabled: loading,\n            children: loading ? \"Sending...\" : \"Send Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this), responseMessage && /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"response-message\",\n            style: {\n              marginTop: '1rem',\n              textAlign: 'center',\n              color: '#10b981'\n            },\n            children: responseMessage\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 621,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"footer-text\",\n          children: \"\\xA9 2024 BrainWave Educational Platform. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 95,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"vVtYmt17VF9hemTk2lWQnWXPyEA=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Link", "motion", "TbArrowBigRightLinesFilled", "TbBrain", "TbBook", "TbTrophy", "TbUsers", "TbStar", "TbSchool", "message", "useDispatch", "useSelector", "HideLoading", "ShowLoading", "getAllReviews", "Image1", "contactUs", "NotificationBell", "ProfilePicture", "OptimizedImage", "jsxDEV", "_jsxDEV", "Home", "_s", "homeSectionRef", "reviewsSectionRef", "contactUsRef", "dispatch", "formData", "setFormData", "name", "email", "loading", "setLoading", "responseMessage", "setResponseMessage", "user", "state", "isLoggedIn", "timer", "setTimeout", "getReviews", "clearTimeout", "response", "success", "setReviews", "data", "error", "scrollToSection", "ref", "offset", "current", "sectionTop", "offsetTop", "window", "scrollTo", "top", "behavior", "setMenuOpen", "handleChange", "e", "value", "target", "handleSubmit", "preventDefault", "className", "children", "nav", "initial", "y", "opacity", "animate", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "style", "fontFamily", "letterSpacing", "span", "x", "scale", "textShadow", "transition", "duration", "delay", "repeat", "Infinity", "ease", "whileHover", "rotate", "color", "fontWeight", "div", "backgroundColor", "boxShadow", "width", "background", "src", "alt", "onError", "display", "nextS<PERSON>ling", "isAdmin", "size", "showOnlineStatus", "height", "class", "menuOpen", "exit", "LazyWrapper", "left", "bottom", "right", "whileInView", "viewport", "once", "number", "text", "map", "stat", "index", "rating", "review", "_review$user", "fontSize", "onSubmit", "type", "placeholder", "onChange", "required", "disabled", "marginTop", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Home/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport \"./index.css\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  TbArrowBigRightLinesFilled,\r\n  TbBrain,\r\n  TbBook,\r\n  TbTrophy,\r\n  TbUsers,\r\n  TbStar,\r\n  TbSchool\r\n} from \"react-icons/tb\";\r\nimport { message } from \"antd\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { getAllReviews } from \"../../../apicalls/reviews\";\r\nimport Image1 from \"../../../assets/collage-1.png\";\r\nimport { contactUs } from \"../../../apicalls/users\";\r\nimport NotificationBell from \"../../../components/common/NotificationBell\";\r\nimport ProfilePicture from \"../../../components/common/ProfilePicture\";\r\nimport { OptimizedImage } from \"../../../components/modern/PerformanceMonitor\";\r\n\r\nconst Home = () => {\r\n  const homeSectionRef = useRef(null);\r\n  const reviewsSectionRef = useRef(null);\r\n  const contactUsRef = useRef(null);\r\n  const dispatch = useDispatch();\r\n  const [formData, setFormData] = useState({ name: \"\", email: \"\", message: \"\" });\r\n  const [loading, setLoading] = useState(false);\r\n  const [responseMessage, setResponseMessage] = useState(\"\");\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  // Check if user is logged in\r\n  const isLoggedIn = !!user;\r\n\r\n  // Optimize reviews loading - only fetch when reviews section is visible\r\n  useEffect(() => {\r\n    // Delay reviews loading to improve initial page load\r\n    const timer = setTimeout(() => {\r\n      getReviews();\r\n    }, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  const getReviews = async () => {\r\n    dispatch(ShowLoading());\r\n    try {\r\n      const response = await getAllReviews();\r\n      if (response.success) {\r\n        setReviews(response.data);\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(error.message);\r\n    }\r\n    dispatch(HideLoading());\r\n  };\r\n\r\n  const scrollToSection = (ref, offset = 80) => {\r\n    if (ref?.current) {\r\n      const sectionTop = ref.current.offsetTop;\r\n      window.scrollTo({ top: sectionTop - offset, behavior: \"smooth\" });\r\n      // Close mobile menu after clicking\r\n      setMenuOpen(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({ ...formData, [name]: value });\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setResponseMessage(\"\");\r\n    try {\r\n      const data = await contactUs(formData);\r\n      if (data.success) {\r\n        message.success(\"Message sent successfully!\");\r\n        setResponseMessage(\"Message sent successfully!\");\r\n        setFormData({ name: \"\", email: \"\", message: \"\" });\r\n      } else {\r\n        setResponseMessage(data.message || \"Something went wrong.\");\r\n      }\r\n    } catch (error) {\r\n      setResponseMessage(\"Error sending message. Please try again.\");\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"Home\">\r\n      {/* Navigation */}\r\n      <motion.nav\r\n        initial={{ y: -20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        className=\"nav-header fixed w-full top-0 z-50\"\r\n      >\r\n        <div className=\"container\">\r\n          <div className=\"flex items-center justify-between h-16\">\r\n            {/* Left Section - Navigation */}\r\n            <div className=\"hidden md:flex items-center space-x-6\">\r\n              <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n              <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n              <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n            </div>\r\n\r\n            {/* Center Section - Logo */}\r\n            <div className=\"flex-1 flex justify-center\">\r\n              <Link to=\"/\" className=\"flex items-center space-x-2 sm:space-x-3\">\r\n                {/* Tanzania Flag */}\r\n                <div className=\"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white\">\r\n                  <div className=\"w-full h-full bg-gradient-to-b from-green-500 via-yellow-400 to-blue-500 relative\">\r\n                    <div className=\"absolute inset-0 bg-black/20\"></div>\r\n                    <div className=\"absolute top-0 left-0 w-full h-1/3 bg-green-500\"></div>\r\n                    <div className=\"absolute top-1/3 left-0 w-full h-1/3 bg-yellow-400\"></div>\r\n                    <div className=\"absolute bottom-0 left-0 w-full h-1/3 bg-blue-500\"></div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Amazing Animated Brainwave Text */}\r\n                <div className=\"relative brainwave-container\">\r\n                  <h1 className=\"text-xl sm:text-2xl md:text-3xl font-black tracking-tight relative z-10 select-none\"\r\n                      style={{\r\n                        fontFamily: \"'Inter', 'SF Pro Display', 'Helvetica Neue', sans-serif\",\r\n                        letterSpacing: '-0.02em'\r\n                      }}>\r\n                    {/* Brain - with amazing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block\"\r\n                      initial={{ opacity: 0, x: -30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\",\r\n                          \"0 0 20px rgba(59, 130, 246, 0.8)\",\r\n                          \"0 0 10px rgba(59, 130, 246, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.3,\r\n                        textShadow: {\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\"\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, -2, 2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#1f2937',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'\r\n                      }}\r\n                    >\r\n                      Brain\r\n\r\n                      {/* Electric spark */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -right-1 w-2 h-2 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1.2, 0.5],\r\n                          backgroundColor: ['#3b82f6', '#60a5fa', '#3b82f6']\r\n                        }}\r\n                        transition={{\r\n                          duration: 1.5,\r\n                          repeat: Infinity,\r\n                          delay: 2\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#3b82f6',\r\n                          boxShadow: '0 0 10px #3b82f6'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n\r\n                    {/* Wave - with flowing effects */}\r\n                    <motion.span\r\n                      className=\"relative inline-block ml-1\"\r\n                      initial={{ opacity: 0, x: 30, scale: 0.8 }}\r\n                      animate={{\r\n                        opacity: 1,\r\n                        x: 0,\r\n                        scale: 1,\r\n                        textShadow: [\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\",\r\n                          \"0 0 20px rgba(16, 185, 129, 0.8)\",\r\n                          \"0 0 10px rgba(16, 185, 129, 0.5)\"\r\n                        ]\r\n                      }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        delay: 0.6,\r\n                        textShadow: {\r\n                          duration: 2.5,\r\n                          repeat: Infinity,\r\n                          ease: \"easeInOut\",\r\n                          delay: 0.5\r\n                        }\r\n                      }}\r\n                      whileHover={{\r\n                        scale: 1.1,\r\n                        rotate: [0, 2, -2, 0],\r\n                        transition: { duration: 0.3 }\r\n                      }}\r\n                      style={{\r\n                        color: '#10b981',\r\n                        fontWeight: '900',\r\n                        textShadow: '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      }}\r\n                    >\r\n                      wave\r\n\r\n                      {/* Wave particles */}\r\n                      <motion.div\r\n                        className=\"absolute -top-1 -left-1 w-1 h-1 rounded-full\"\r\n                        animate={{\r\n                          opacity: [0, 1, 0],\r\n                          scale: [0.5, 1, 0.5],\r\n                          x: [0, 10, 0],\r\n                          backgroundColor: ['#10b981', '#34d399', '#10b981']\r\n                        }}\r\n                        transition={{\r\n                          duration: 2,\r\n                          repeat: Infinity,\r\n                          delay: 3\r\n                        }}\r\n                        style={{\r\n                          backgroundColor: '#10b981',\r\n                          boxShadow: '0 0 8px #10b981'\r\n                        }}\r\n                      />\r\n                    </motion.span>\r\n                  </h1>\r\n\r\n                  {/* Glowing underline effect */}\r\n                  <motion.div\r\n                    className=\"absolute -bottom-1 left-0 h-1 rounded-full\"\r\n                    initial={{ width: 0, opacity: 0 }}\r\n                    animate={{\r\n                      width: '100%',\r\n                      opacity: 1,\r\n                      boxShadow: [\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)',\r\n                        '0 0 20px rgba(59, 130, 246, 0.8)',\r\n                        '0 0 10px rgba(16, 185, 129, 0.5)'\r\n                      ]\r\n                    }}\r\n                    transition={{\r\n                      duration: 1.5,\r\n                      delay: 1.2,\r\n                      boxShadow: {\r\n                        duration: 2,\r\n                        repeat: Infinity,\r\n                        ease: \"easeInOut\"\r\n                      }\r\n                    }}\r\n                    style={{\r\n                      background: 'linear-gradient(90deg, #3b82f6, #10b981, #3b82f6)',\r\n                      boxShadow: '0 0 15px rgba(16, 185, 129, 0.6)'\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Official Logo */}\r\n                <div className=\"w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full overflow-hidden shadow-lg border-2 border-white\">\r\n                  <img\r\n                    src=\"/favicon.png\"\r\n                    alt=\"Brainwave Logo\"\r\n                    className=\"w-full h-full object-cover\"\r\n                    onError={(e) => {\r\n                      e.target.style.display = 'none';\r\n                      e.target.nextSibling.style.display = 'flex';\r\n                    }}\r\n                  />\r\n                  <div className=\"w-full h-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-sm sm:text-base md:text-lg\" style={{display: 'none'}}>\r\n                    🧠\r\n                  </div>\r\n                </div>\r\n              </Link>\r\n            </div>\r\n\r\n            {/* Right Section - Only Notifications and Profile */}\r\n            <div className=\"flex items-center space-x-3 sm:space-x-4\">\r\n              {/* Notification Bell - Only show if logged in and not admin */}\r\n              {isLoggedIn && !user?.isAdmin && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.2 }}\r\n                >\r\n                  <NotificationBell />\r\n                </motion.div>\r\n              )}\r\n\r\n              {/* User Profile - Ranking Style and Responsive */}\r\n              {isLoggedIn && (\r\n                <motion.div\r\n                  initial={{ opacity: 0, scale: 0.8 }}\r\n                  animate={{ opacity: 1, scale: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.3 }}\r\n                  className=\"flex items-center space-x-2 sm:space-x-3 group cursor-pointer\"\r\n                >\r\n                  {/* Profile Picture - Ranking Style */}\r\n                  <div className=\"relative\">\r\n                    <ProfilePicture\r\n                      user={user}\r\n                      size=\"sm\"\r\n                      showOnlineStatus={true}\r\n                      className=\"ring-2 ring-blue-200 hover:ring-blue-300 transition-all duration-300\"\r\n                      style={{\r\n                        width: '36px',\r\n                        height: '36px',\r\n                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.15)'\r\n                      }}\r\n                    />\r\n                  </div>\r\n\r\n                  {/* User Details - Responsive */}\r\n                  <div className=\"hidden sm:block text-right\">\r\n                    <div className=\"text-sm font-semibold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 truncate max-w-[120px] lg:max-w-[160px]\">\r\n                      {user?.name || 'User'}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 group-hover:text-blue-500 transition-colors duration-300\">\r\n                      {user?.isAdmin ? 'Administrator' : `Class ${user?.class || 'N/A'}`}\r\n                    </div>\r\n                  </div>\r\n                </motion.div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Navigation */}\r\n          {menuOpen && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              exit={{ opacity: 0, y: -20 }}\r\n              className=\"md:hidden mobile-nav\"\r\n            >\r\n              <div className=\"flex flex-col\">\r\n                <button onClick={() => scrollToSection(homeSectionRef)} className=\"nav-item\">Home</button>\r\n                <button onClick={() => scrollToSection(reviewsSectionRef)} className=\"nav-item\">Reviews</button>\r\n                <button onClick={() => scrollToSection(contactUsRef)} className=\"nav-item\">Contact Us</button>\r\n\r\n                {/* Mobile Login/Register - Only show if not logged in */}\r\n                {!isLoggedIn && (\r\n                  <div className=\"flex flex-col space-y-2 mt-4 pt-4 border-t border-gray-200\">\r\n                    <Link\r\n                      to=\"/login\"\r\n                      className=\"nav-item text-center\"\r\n                      onClick={() => setMenuOpen(false)}\r\n                    >\r\n                      Login\r\n                    </Link>\r\n                    <Link\r\n                      to=\"/register\"\r\n                      className=\"nav-item text-center bg-blue-600 text-white rounded-lg\"\r\n                      onClick={() => setMenuOpen(false)}\r\n                    >\r\n                      Register\r\n                    </Link>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      </motion.nav>\r\n\r\n      {/* Hero Section */}\r\n      <section ref={homeSectionRef} className=\"hero-section\">\r\n        <div className=\"container\">\r\n          <div className=\"hero-grid\">\r\n            {/* Hero Content */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: -50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              className=\"hero-content\"\r\n            >\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.2 }}\r\n                className=\"hero-badge\"\r\n              >\r\n                <TbSchool className=\"w-5 h-5 mr-2\" />\r\n                #1 Educational Platform in Tanzania\r\n              </motion.div>\r\n\r\n              <h1 className=\"hero-title\">\r\n                Fueling Bright Futures with{\" \"}\r\n                <span className=\"text-gradient\">\r\n                  Education\r\n                  <TbArrowBigRightLinesFilled className=\"inline w-8 h-8 ml-2\" />\r\n                </span>\r\n              </h1>\r\n\r\n              <p className=\"hero-subtitle\">\r\n                Discover limitless learning opportunities with our comprehensive\r\n                online study platform. Study anywhere, anytime, and achieve your\r\n                academic goals with confidence.\r\n              </p>\r\n\r\n              {/* Explore Platform Button */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.4 }}\r\n                className=\"cta-section\"\r\n              >\r\n                <Link to=\"/login\">\r\n                  <button className=\"btn btn-primary btn-large\">\r\n                    <TbBrain className=\"w-5 h-5 mr-2\" />\r\n                    Explore Platform\r\n                  </button>\r\n                </Link>\r\n              </motion.div>\r\n\r\n              {/* Trust Indicators */}\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: 0.6 }}\r\n                className=\"trust-indicators\"\r\n              >\r\n                <div className=\"trust-indicator\">\r\n                  <TbUsers style={{color: '#007BFF'}} />\r\n                  <span>15K+ Students</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbStar style={{color: '#f59e0b'}} />\r\n                  <span>4.9/5 Rating</span>\r\n                </div>\r\n                <div className=\"trust-indicator\">\r\n                  <TbTrophy style={{color: '#007BFF'}} />\r\n                  <span>Award Winning</span>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Hero Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              className=\"hero-image\"\r\n            >\r\n              <div className=\"relative\">\r\n                <LazyWrapper>\r\n                  <OptimizedImage\r\n                    src={Image1}\r\n                    alt=\"Students Learning\"\r\n                    className=\"w-full h-auto\"\r\n                    loading=\"lazy\"\r\n                  />\r\n                </LazyWrapper>\r\n\r\n                {/* Floating Elements */}\r\n                <motion.div\r\n                  animate={{ y: [-10, 10, -10] }}\r\n                  transition={{ duration: 4, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{top: '-1rem', left: '-1rem'}}\r\n                >\r\n                  <TbBook style={{color: '#007BFF'}} />\r\n                </motion.div>\r\n\r\n                <motion.div\r\n                  animate={{ y: [10, -10, 10] }}\r\n                  transition={{ duration: 3, repeat: Infinity }}\r\n                  className=\"floating-element\"\r\n                  style={{bottom: '-1rem', right: '-1rem'}}\r\n                >\r\n                  <TbTrophy style={{color: '#f59e0b'}} />\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Stats Section */}\r\n      <section className=\"stats-section\">\r\n        <div className=\"container\">\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 50 }}\r\n            whileInView={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"stats-grid\"\r\n          >\r\n            {[\r\n              { number: \"15K+\", text: \"Active Students\" },\r\n              { number: \"500+\", text: \"Expert Teachers\" },\r\n              { number: \"1000+\", text: \"Video Lessons\" },\r\n              { number: \"98%\", text: \"Success Rate\" }\r\n            ].map((stat, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"stat-item\"\r\n              >\r\n                <div className=\"stat-number\">{stat.number}</div>\r\n                <p className=\"stat-text\">{stat.text}</p>\r\n              </motion.div>\r\n            ))}\r\n          </motion.div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Reviews Section */}\r\n      <section ref={reviewsSectionRef} className=\"reviews-section\">\r\n        <div className=\"reviews-container\">\r\n          <h2 className=\"reviews-title\">\r\n            Reviews from our students\r\n          </h2>\r\n          <div className=\"reviews-grid\">\r\n            {[\r\n              {\r\n                rating: 5,\r\n                text: \"BrainWave has completely transformed my learning experience. The interactive lessons and expert guidance helped me excel in my studies.\",\r\n                user: { name: \"Sarah Johnson\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"The platform is incredibly user-friendly and the content quality is outstanding. I've improved my grades significantly since joining.\",\r\n                user: { name: \"Michael Chen\" }\r\n              },\r\n              {\r\n                rating: 5,\r\n                text: \"Amazing platform with excellent teachers. The video lessons are clear and easy to understand. Highly recommended!\",\r\n                user: { name: \"Amina Hassan\" }\r\n              }\r\n            ].map((review, index) => (\r\n              <motion.div\r\n                key={index}\r\n                initial={{ opacity: 0, y: 30 }}\r\n                whileInView={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.6, delay: index * 0.1 }}\r\n                viewport={{ once: true }}\r\n                className=\"review-card\"\r\n              >\r\n                <div className=\"review-rating\">\r\n                  <div style={{ color: '#f59e0b', fontSize: '1.25rem' }}>\r\n                    {'★'.repeat(review.rating)}\r\n                  </div>\r\n                </div>\r\n                <div className=\"review-text\">\"{review.text}\"</div>\r\n                <div className=\"review-divider\"></div>\r\n                <div className=\"review-author\">{review.user?.name}</div>\r\n              </motion.div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Section */}\r\n      <section ref={contactUsRef} className=\"contact-section\">\r\n        <div className=\"contact-container\">\r\n          <h2 className=\"contact-title\">Contact Us</h2>\r\n          <p className=\"contact-subtitle\">Get in touch with us for any questions or support</p>\r\n          <form className=\"contact-form\" onSubmit={handleSubmit}>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Name</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"name\"\r\n                placeholder=\"Your Name\"\r\n                className=\"form-input\"\r\n                value={formData.name}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Email</label>\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                placeholder=\"Your Email\"\r\n                className=\"form-input\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                required\r\n              />\r\n            </div>\r\n            <div className=\"form-group\">\r\n              <label className=\"form-label\">Message</label>\r\n              <textarea\r\n                name=\"message\"\r\n                placeholder=\"Your Message\"\r\n                className=\"form-input form-textarea\"\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                required\r\n              ></textarea>\r\n            </div>\r\n            <button\r\n              type=\"submit\"\r\n              className=\"form-submit\"\r\n              disabled={loading}\r\n            >\r\n              {loading ? \"Sending...\" : \"Send Message\"}\r\n            </button>\r\n            {responseMessage && (\r\n              <p className=\"response-message\" style={{ marginTop: '1rem', textAlign: 'center', color: '#10b981' }}>\r\n                {responseMessage}\r\n              </p>\r\n            )}\r\n          </form>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"footer\">\r\n        <div className=\"footer-content\">\r\n          <p className=\"footer-text\">\r\n            © 2024 BrainWave Educational Platform. All rights reserved.\r\n          </p>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,0BAA0B,EAC1BC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,OAAO,EACPC,MAAM,EACNC,QAAQ,QACH,gBAAgB;AACvB,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,aAAa,QAAQ,2BAA2B;AACzD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAOC,gBAAgB,MAAM,6CAA6C;AAC1E,OAAOC,cAAc,MAAM,2CAA2C;AACtE,SAASC,cAAc,QAAQ,+CAA+C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,cAAc,GAAGzB,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM0B,iBAAiB,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACtC,MAAM2B,YAAY,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM4B,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IAAEiC,IAAI,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEtB,OAAO,EAAE;EAAG,CAAC,CAAC;EAC9E,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM;IAAEuC;EAAK,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA,MAAME,UAAU,GAAG,CAAC,CAACF,IAAI;;EAEzB;EACAtC,SAAS,CAAC,MAAM;IACd;IACA,MAAMyC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BC,UAAU,CAAC,CAAC;IACd,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,YAAY,CAACH,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7Bd,QAAQ,CAACd,WAAW,CAAC,CAAC,CAAC;IACvB,IAAI;MACF,MAAM8B,QAAQ,GAAG,MAAM7B,aAAa,CAAC,CAAC;MACtC,IAAI6B,QAAQ,CAACC,OAAO,EAAE;QACpBC,UAAU,CAACF,QAAQ,CAACG,IAAI,CAAC;MAC3B,CAAC,MAAM;QACLrC,OAAO,CAACsC,KAAK,CAACJ,QAAQ,CAAClC,OAAO,CAAC;MACjC;IACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAACA,KAAK,CAACtC,OAAO,CAAC;IAC9B;IACAkB,QAAQ,CAACf,WAAW,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAMoC,eAAe,GAAGA,CAACC,GAAG,EAAEC,MAAM,GAAG,EAAE,KAAK;IAC5C,IAAID,GAAG,aAAHA,GAAG,eAAHA,GAAG,CAAEE,OAAO,EAAE;MAChB,MAAMC,UAAU,GAAGH,GAAG,CAACE,OAAO,CAACE,SAAS;MACxCC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAEJ,UAAU,GAAGF,MAAM;QAAEO,QAAQ,EAAE;MAAS,CAAC,CAAC;MACjE;MACAC,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAE9B,IAAI;MAAE+B;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChCjC,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACE,IAAI,GAAG+B;IAAM,CAAC,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClB/B,UAAU,CAAC,IAAI,CAAC;IAChBE,kBAAkB,CAAC,EAAE,CAAC;IACtB,IAAI;MACF,MAAMW,IAAI,GAAG,MAAM9B,SAAS,CAACY,QAAQ,CAAC;MACtC,IAAIkB,IAAI,CAACF,OAAO,EAAE;QAChBnC,OAAO,CAACmC,OAAO,CAAC,4BAA4B,CAAC;QAC7CT,kBAAkB,CAAC,4BAA4B,CAAC;QAChDN,WAAW,CAAC;UAAEC,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,EAAE;UAAEtB,OAAO,EAAE;QAAG,CAAC,CAAC;MACnD,CAAC,MAAM;QACL0B,kBAAkB,CAACW,IAAI,CAACrC,OAAO,IAAI,uBAAuB,CAAC;MAC7D;IACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdZ,kBAAkB,CAAC,0CAA0C,CAAC;IAChE;IACAF,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEZ,OAAA;IAAK4C,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAEnB7C,OAAA,CAACpB,MAAM,CAACkE,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,EAAE;QAAEC,OAAO,EAAE;MAAE,CAAE;MAChCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BL,SAAS,EAAC,oCAAoC;MAAAC,QAAA,eAE9C7C,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxB7C,OAAA;UAAK4C,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBAErD7C,OAAA;YAAK4C,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpD7C,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACxB,cAAc,CAAE;cAACyC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1FvD,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACvB,iBAAiB,CAAE;cAACwC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChGvD,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACtB,YAAY,CAAE;cAACuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,eAGNvD,OAAA;YAAK4C,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzC7C,OAAA,CAACrB,IAAI;cAAC6E,EAAE,EAAC,GAAG;cAACZ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,gBAE/D7C,OAAA;gBAAK4C,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjH7C,OAAA;kBAAK4C,SAAS,EAAC,mFAAmF;kBAAAC,QAAA,gBAChG7C,OAAA;oBAAK4C,SAAS,EAAC;kBAA8B;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpDvD,OAAA;oBAAK4C,SAAS,EAAC;kBAAiD;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvEvD,OAAA;oBAAK4C,SAAS,EAAC;kBAAoD;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1EvD,OAAA;oBAAK4C,SAAS,EAAC;kBAAmD;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNvD,OAAA;gBAAK4C,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,gBAC3C7C,OAAA;kBAAI4C,SAAS,EAAC,qFAAqF;kBAC/Fa,KAAK,EAAE;oBACLC,UAAU,EAAE,yDAAyD;oBACrEC,aAAa,EAAE;kBACjB,CAAE;kBAAAd,QAAA,gBAEJ7C,OAAA,CAACpB,MAAM,CAACgF,IAAI;oBACVhB,SAAS,EAAC,uBAAuB;oBACjCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEY,CAAC,EAAE,CAAC,EAAE;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC5CZ,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVY,CAAC,EAAE,CAAC;sBACJC,KAAK,EAAE,CAAC;sBACRC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFC,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVH,UAAU,EAAE;wBACVE,QAAQ,EAAE,CAAC;wBACXE,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE;sBACR;oBACF,CAAE;oBACFC,UAAU,EAAE;sBACVR,KAAK,EAAE,GAAG;sBACVS,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;sBACrBP,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFR,KAAK,EAAE;sBACLe,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBV,UAAU,EAAE;oBACd,CAAE;oBAAAlB,QAAA,GACH,OAGC,eACA7C,OAAA,CAACpB,MAAM,CAAC8F,GAAG;sBACT9B,SAAS,EAAC,+CAA+C;sBACzDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBa,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;wBACtBa,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFX,UAAU,EAAE;wBACVC,QAAQ,EAAE,GAAG;wBACbE,MAAM,EAAEC,QAAQ;wBAChBF,KAAK,EAAE;sBACT,CAAE;sBACFT,KAAK,EAAE;wBACLkB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC,eAGdvD,OAAA,CAACpB,MAAM,CAACgF,IAAI;oBACVhB,SAAS,EAAC,4BAA4B;oBACtCG,OAAO,EAAE;sBAAEE,OAAO,EAAE,CAAC;sBAAEY,CAAC,EAAE,EAAE;sBAAEC,KAAK,EAAE;oBAAI,CAAE;oBAC3CZ,OAAO,EAAE;sBACPD,OAAO,EAAE,CAAC;sBACVY,CAAC,EAAE,CAAC;sBACJC,KAAK,EAAE,CAAC;sBACRC,UAAU,EAAE,CACV,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;oBAEtC,CAAE;oBACFC,UAAU,EAAE;sBACVC,QAAQ,EAAE,CAAC;sBACXC,KAAK,EAAE,GAAG;sBACVH,UAAU,EAAE;wBACVE,QAAQ,EAAE,GAAG;wBACbE,MAAM,EAAEC,QAAQ;wBAChBC,IAAI,EAAE,WAAW;wBACjBH,KAAK,EAAE;sBACT;oBACF,CAAE;oBACFI,UAAU,EAAE;sBACVR,KAAK,EAAE,GAAG;sBACVS,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;sBACrBP,UAAU,EAAE;wBAAEC,QAAQ,EAAE;sBAAI;oBAC9B,CAAE;oBACFR,KAAK,EAAE;sBACLe,KAAK,EAAE,SAAS;sBAChBC,UAAU,EAAE,KAAK;sBACjBV,UAAU,EAAE;oBACd,CAAE;oBAAAlB,QAAA,GACH,MAGC,eACA7C,OAAA,CAACpB,MAAM,CAAC8F,GAAG;sBACT9B,SAAS,EAAC,8CAA8C;sBACxDM,OAAO,EAAE;wBACPD,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAClBa,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;wBACpBD,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;wBACbc,eAAe,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBACnD,CAAE;sBACFX,UAAU,EAAE;wBACVC,QAAQ,EAAE,CAAC;wBACXE,MAAM,EAAEC,QAAQ;wBAChBF,KAAK,EAAE;sBACT,CAAE;sBACFT,KAAK,EAAE;wBACLkB,eAAe,EAAE,SAAS;wBAC1BC,SAAS,EAAE;sBACb;oBAAE;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eAGLvD,OAAA,CAACpB,MAAM,CAAC8F,GAAG;kBACT9B,SAAS,EAAC,4CAA4C;kBACtDG,OAAO,EAAE;oBAAE8B,KAAK,EAAE,CAAC;oBAAE5B,OAAO,EAAE;kBAAE,CAAE;kBAClCC,OAAO,EAAE;oBACP2B,KAAK,EAAE,MAAM;oBACb5B,OAAO,EAAE,CAAC;oBACV2B,SAAS,EAAE,CACT,kCAAkC,EAClC,kCAAkC,EAClC,kCAAkC;kBAEtC,CAAE;kBACFZ,UAAU,EAAE;oBACVC,QAAQ,EAAE,GAAG;oBACbC,KAAK,EAAE,GAAG;oBACVU,SAAS,EAAE;sBACTX,QAAQ,EAAE,CAAC;sBACXE,MAAM,EAAEC,QAAQ;sBAChBC,IAAI,EAAE;oBACR;kBACF,CAAE;kBACFZ,KAAK,EAAE;oBACLqB,UAAU,EAAE,mDAAmD;oBAC/DF,SAAS,EAAE;kBACb;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNvD,OAAA;gBAAK4C,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,gBACjH7C,OAAA;kBACE+E,GAAG,EAAC,cAAc;kBAClBC,GAAG,EAAC,gBAAgB;kBACpBpC,SAAS,EAAC,4BAA4B;kBACtCqC,OAAO,EAAG1C,CAAC,IAAK;oBACdA,CAAC,CAACE,MAAM,CAACgB,KAAK,CAACyB,OAAO,GAAG,MAAM;oBAC/B3C,CAAC,CAACE,MAAM,CAAC0C,WAAW,CAAC1B,KAAK,CAACyB,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFvD,OAAA;kBAAK4C,SAAS,EAAC,sIAAsI;kBAACa,KAAK,EAAE;oBAACyB,OAAO,EAAE;kBAAM,CAAE;kBAAArC,QAAA,EAAC;gBAEhL;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNvD,OAAA;YAAK4C,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAEtD5B,UAAU,IAAI,EAACF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqE,OAAO,kBAC3BpF,OAAA,CAACpB,MAAM,CAAC8F,GAAG;cACT3B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAI,CAAE;cACpCZ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAE,CAAE;cAClCE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAAArB,QAAA,eAE1C7C,OAAA,CAACJ,gBAAgB;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACb,EAGAtC,UAAU,iBACTjB,OAAA,CAACpB,MAAM,CAAC8F,GAAG;cACT3B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAI,CAAE;cACpCZ,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAEa,KAAK,EAAE;cAAE,CAAE;cAClCE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CtB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAGzE7C,OAAA;gBAAK4C,SAAS,EAAC,UAAU;gBAAAC,QAAA,eACvB7C,OAAA,CAACH,cAAc;kBACbkB,IAAI,EAAEA,IAAK;kBACXsE,IAAI,EAAC,IAAI;kBACTC,gBAAgB,EAAE,IAAK;kBACvB1C,SAAS,EAAC,sEAAsE;kBAChFa,KAAK,EAAE;oBACLoB,KAAK,EAAE,MAAM;oBACbU,MAAM,EAAE,MAAM;oBACdX,SAAS,EAAE;kBACb;gBAAE;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGNvD,OAAA;gBAAK4C,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzC7C,OAAA;kBAAK4C,SAAS,EAAC,sIAAsI;kBAAAC,QAAA,EAClJ,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEN,IAAI,KAAI;gBAAM;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNvD,OAAA;kBAAK4C,SAAS,EAAC,gFAAgF;kBAAAC,QAAA,EAC5F9B,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEqE,OAAO,GAAG,eAAe,GAAI,SAAQ,CAAArE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,KAAK,KAAI,KAAM;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACb;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLkC,QAAQ,iBACPzF,OAAA,CAACpB,MAAM,CAAC8F,GAAG;UACT3B,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCE,OAAO,EAAE;YAAED,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAC9B0C,IAAI,EAAE;YAAEzC,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BJ,SAAS,EAAC,sBAAsB;UAAAC,QAAA,eAEhC7C,OAAA;YAAK4C,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B7C,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACxB,cAAc,CAAE;cAACyC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1FvD,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACvB,iBAAiB,CAAE;cAACwC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChGvD,OAAA;cAAQmD,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACtB,YAAY,CAAE;cAACuC,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAU;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAG7F,CAACtC,UAAU,iBACVjB,OAAA;cAAK4C,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACzE7C,OAAA,CAACrB,IAAI;gBACH6E,EAAE,EAAC,QAAQ;gBACXZ,SAAS,EAAC,sBAAsB;gBAChCO,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC,KAAK,CAAE;gBAAAQ,QAAA,EACnC;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPvD,OAAA,CAACrB,IAAI;gBACH6E,EAAE,EAAC,WAAW;gBACdZ,SAAS,EAAC,wDAAwD;gBAClEO,OAAO,EAAEA,CAAA,KAAMd,WAAW,CAAC,KAAK,CAAE;gBAAAQ,QAAA,EACnC;cAED;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGbvD,OAAA;MAAS4B,GAAG,EAAEzB,cAAe;MAACyC,SAAS,EAAC,cAAc;MAAAC,QAAA,eACpD7C,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB7C,OAAA;UAAK4C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB7C,OAAA,CAACpB,MAAM,CAAC8F,GAAG;YACT3B,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCX,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BrB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExB7C,OAAA,CAACpB,MAAM,CAAC8F,GAAG;cACT3B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BgB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CtB,SAAS,EAAC,YAAY;cAAAC,QAAA,gBAEtB7C,OAAA,CAACb,QAAQ;gBAACyD,SAAS,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uCAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbvD,OAAA;cAAI4C,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,6BACE,EAAC,GAAG,eAC/B7C,OAAA;gBAAM4C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAC,WAE9B,eAAA7C,OAAA,CAACnB,0BAA0B;kBAAC+D,SAAS,EAAC;gBAAqB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELvD,OAAA;cAAG4C,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAI7B;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJvD,OAAA,CAACpB,MAAM,CAAC8F,GAAG;cACT3B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BgB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CtB,SAAS,EAAC,aAAa;cAAAC,QAAA,eAEvB7C,OAAA,CAACrB,IAAI;gBAAC6E,EAAE,EAAC,QAAQ;gBAAAX,QAAA,eACf7C,OAAA;kBAAQ4C,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBAC3C7C,OAAA,CAAClB,OAAO;oBAAC8D,SAAS,EAAC;kBAAc;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,oBAEtC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eAGbvD,OAAA,CAACpB,MAAM,CAAC8F,GAAG;cACT3B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/BE,OAAO,EAAE;gBAAED,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAC9BgB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAE;cAAI,CAAE;cAC1CtB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAE5B7C,OAAA;gBAAK4C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B7C,OAAA,CAACf,OAAO;kBAACwE,KAAK,EAAE;oBAACe,KAAK,EAAE;kBAAS;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCvD,OAAA;kBAAA6C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACNvD,OAAA;gBAAK4C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B7C,OAAA,CAACd,MAAM;kBAACuE,KAAK,EAAE;oBAACe,KAAK,EAAE;kBAAS;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrCvD,OAAA;kBAAA6C,QAAA,EAAM;gBAAY;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eACNvD,OAAA;gBAAK4C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9B7C,OAAA,CAAChB,QAAQ;kBAACyE,KAAK,EAAE;oBAACe,KAAK,EAAE;kBAAS;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvCvD,OAAA;kBAAA6C,QAAA,EAAM;gBAAa;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGbvD,OAAA,CAACpB,MAAM,CAAC8F,GAAG;YACT3B,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAG,CAAE;YAC/BX,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEY,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAI,CAAE;YAC1CtB,SAAS,EAAC,YAAY;YAAAC,QAAA,eAEtB7C,OAAA;cAAK4C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB7C,OAAA,CAAC2F,WAAW;gBAAA9C,QAAA,eACV7C,OAAA,CAACF,cAAc;kBACbiF,GAAG,EAAErF,MAAO;kBACZsF,GAAG,EAAC,mBAAmB;kBACvBpC,SAAS,EAAC,eAAe;kBACzBjC,OAAO,EAAC;gBAAM;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CAAC,eAGdvD,OAAA,CAACpB,MAAM,CAAC8F,GAAG;gBACTxB,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;gBAAE,CAAE;gBAC/BgB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEE,MAAM,EAAEC;gBAAS,CAAE;gBAC9CxB,SAAS,EAAC,kBAAkB;gBAC5Ba,KAAK,EAAE;kBAACtB,GAAG,EAAE,OAAO;kBAAEyD,IAAI,EAAE;gBAAO,CAAE;gBAAA/C,QAAA,eAErC7C,OAAA,CAACjB,MAAM;kBAAC0E,KAAK,EAAE;oBAACe,KAAK,EAAE;kBAAS;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eAEbvD,OAAA,CAACpB,MAAM,CAAC8F,GAAG;gBACTxB,OAAO,EAAE;kBAAEF,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE;gBAAE,CAAE;gBAC9BgB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEE,MAAM,EAAEC;gBAAS,CAAE;gBAC9CxB,SAAS,EAAC,kBAAkB;gBAC5Ba,KAAK,EAAE;kBAACoC,MAAM,EAAE,OAAO;kBAAEC,KAAK,EAAE;gBAAO,CAAE;gBAAAjD,QAAA,eAEzC7C,OAAA,CAAChB,QAAQ;kBAACyE,KAAK,EAAE;oBAACe,KAAK,EAAE;kBAAS;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAS4C,SAAS,EAAC,eAAe;MAAAC,QAAA,eAChC7C,OAAA;QAAK4C,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxB7C,OAAA,CAACpB,MAAM,CAAC8F,GAAG;UACT3B,OAAO,EAAE;YAAEE,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAG,CAAE;UAC/B+C,WAAW,EAAE;YAAE9C,OAAO,EAAE,CAAC;YAAED,CAAC,EAAE;UAAE,CAAE;UAClCgB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B+B,QAAQ,EAAE;YAAEC,IAAI,EAAE;UAAK,CAAE;UACzBrD,SAAS,EAAC,YAAY;UAAAC,QAAA,EAErB,CACC;YAAEqD,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAkB,CAAC,EAC3C;YAAED,MAAM,EAAE,MAAM;YAAEC,IAAI,EAAE;UAAkB,CAAC,EAC3C;YAAED,MAAM,EAAE,OAAO;YAAEC,IAAI,EAAE;UAAgB,CAAC,EAC1C;YAAED,MAAM,EAAE,KAAK;YAAEC,IAAI,EAAE;UAAe,CAAC,CACxC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChBtG,OAAA,CAACpB,MAAM,CAAC8F,GAAG;YAET3B,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/B+C,WAAW,EAAE;cAAE9C,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAClCgB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEC,KAAK,EAAEoC,KAAK,GAAG;YAAI,CAAE;YAClDN,QAAQ,EAAE;cAAEC,IAAI,EAAE;YAAK,CAAE;YACzBrD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBAErB7C,OAAA;cAAK4C,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEwD,IAAI,CAACH;YAAM;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvD,OAAA;cAAG4C,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAEwD,IAAI,CAACF;YAAI;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GARnC+C,KAAK;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASA,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAS4B,GAAG,EAAExB,iBAAkB;MAACwC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC1D7C,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE9B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvD,OAAA;UAAK4C,SAAS,EAAC,cAAc;UAAAC,QAAA,EAC1B,CACC;YACE0D,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,yIAAyI;YAC/IpF,IAAI,EAAE;cAAEN,IAAI,EAAE;YAAgB;UAChC,CAAC,EACD;YACE8F,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,uIAAuI;YAC7IpF,IAAI,EAAE;cAAEN,IAAI,EAAE;YAAe;UAC/B,CAAC,EACD;YACE8F,MAAM,EAAE,CAAC;YACTJ,IAAI,EAAE,mHAAmH;YACzHpF,IAAI,EAAE;cAAEN,IAAI,EAAE;YAAe;UAC/B,CAAC,CACF,CAAC2F,GAAG,CAAC,CAACI,MAAM,EAAEF,KAAK;YAAA,IAAAG,YAAA;YAAA,oBAClBzG,OAAA,CAACpB,MAAM,CAAC8F,GAAG;cAET3B,OAAO,EAAE;gBAAEE,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAG,CAAE;cAC/B+C,WAAW,EAAE;gBAAE9C,OAAO,EAAE,CAAC;gBAAED,CAAC,EAAE;cAAE,CAAE;cAClCgB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,KAAK,EAAEoC,KAAK,GAAG;cAAI,CAAE;cAClDN,QAAQ,EAAE;gBAAEC,IAAI,EAAE;cAAK,CAAE;cACzBrD,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB7C,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5B7C,OAAA;kBAAKyD,KAAK,EAAE;oBAAEe,KAAK,EAAE,SAAS;oBAAEkC,QAAQ,EAAE;kBAAU,CAAE;kBAAA7D,QAAA,EACnD,GAAG,CAACsB,MAAM,CAACqC,MAAM,CAACD,MAAM;gBAAC;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNvD,OAAA;gBAAK4C,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,IAAC,EAAC2D,MAAM,CAACL,IAAI,EAAC,IAAC;cAAA;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClDvD,OAAA;gBAAK4C,SAAS,EAAC;cAAgB;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtCvD,OAAA;gBAAK4C,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAA4D,YAAA,GAAED,MAAM,CAACzF,IAAI,cAAA0F,YAAA,uBAAXA,YAAA,CAAahG;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GAdnD+C,KAAK;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeA,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAS4B,GAAG,EAAEvB,YAAa;MAACuC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eACrD7C,OAAA;QAAK4C,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7C,OAAA;UAAI4C,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7CvD,OAAA;UAAG4C,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAiD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrFvD,OAAA;UAAM4C,SAAS,EAAC,cAAc;UAAC+D,QAAQ,EAAEjE,YAAa;UAAAG,QAAA,gBACpD7C,OAAA;YAAK4C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7C,OAAA;cAAO4C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1CvD,OAAA;cACE4G,IAAI,EAAC,MAAM;cACXnG,IAAI,EAAC,MAAM;cACXoG,WAAW,EAAC,WAAW;cACvBjE,SAAS,EAAC,YAAY;cACtBJ,KAAK,EAAEjC,QAAQ,CAACE,IAAK;cACrBqG,QAAQ,EAAExE,YAAa;cACvByE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvD,OAAA;YAAK4C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7C,OAAA;cAAO4C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAK;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC3CvD,OAAA;cACE4G,IAAI,EAAC,OAAO;cACZnG,IAAI,EAAC,OAAO;cACZoG,WAAW,EAAC,YAAY;cACxBjE,SAAS,EAAC,YAAY;cACtBJ,KAAK,EAAEjC,QAAQ,CAACG,KAAM;cACtBoG,QAAQ,EAAExE,YAAa;cACvByE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNvD,OAAA;YAAK4C,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB7C,OAAA;cAAO4C,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAO;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7CvD,OAAA;cACES,IAAI,EAAC,SAAS;cACdoG,WAAW,EAAC,cAAc;cAC1BjE,SAAS,EAAC,0BAA0B;cACpCJ,KAAK,EAAEjC,QAAQ,CAACnB,OAAQ;cACxB0H,QAAQ,EAAExE,YAAa;cACvByE,QAAQ;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNvD,OAAA;YACE4G,IAAI,EAAC,QAAQ;YACbhE,SAAS,EAAC,aAAa;YACvBoE,QAAQ,EAAErG,OAAQ;YAAAkC,QAAA,EAEjBlC,OAAO,GAAG,YAAY,GAAG;UAAc;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,EACR1C,eAAe,iBACdb,OAAA;YAAG4C,SAAS,EAAC,kBAAkB;YAACa,KAAK,EAAE;cAAEwD,SAAS,EAAE,MAAM;cAAEC,SAAS,EAAE,QAAQ;cAAE1C,KAAK,EAAE;YAAU,CAAE;YAAA3B,QAAA,EACjGhC;UAAe;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvD,OAAA;MAAQ4C,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACxB7C,OAAA;QAAK4C,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7B7C,OAAA;UAAG4C,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAE3B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACrD,EAAA,CAvmBID,IAAI;EAAA,QAISZ,WAAW,EAIXC,WAAW;AAAA;AAAA6H,EAAA,GARxBlH,IAAI;AAymBV,eAAeA,IAAI;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}